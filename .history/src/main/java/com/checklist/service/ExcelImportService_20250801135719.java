package com.checklist.service;

import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistItem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Excel 导入服务类
 * 提供 Excel 文件解析和模板导入功能
 */
@Service
public class ExcelImportService {
    
    /**
     * 从 Excel 文件导入检查单模板
     * 
     * @param file Excel 文件
     * @param templateName 模板名称
     * @param templateType 模板类型
     * @return 解析后的模板对象
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public ChecklistTemplate importTemplateFromExcel(MultipartFile file, String templateName, String templateType) 
            throws IOException {
        
        // 验证输入参数
        validateImportParameters(file, templateName, templateType);
        
        // 解析 Excel 文件
        List<ChecklistItem> items = parseExcelFile(file);
        
        // 创建模板对象
        ChecklistTemplate template = new ChecklistTemplate();
        template.setId("template_" + UUID.randomUUID().toString().replace("-", ""));
        template.setName(templateName.trim());
        template.setType(templateType.trim());
        template.setVersion("1.0");
        template.setCreatedTime(LocalDateTime.now());
        template.setUpdatedTime(LocalDateTime.now());
        template.setItems(items);
        
        return template;
    }
    
    /**
     * 验证导入参数
     * 
     * @param file Excel 文件
     * @param templateName 模板名称
     * @param templateType 模板类型
     * @throws IllegalArgumentException 参数验证异常
     */
    private void validateImportParameters(MultipartFile file, String templateName, String templateType) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Excel 文件不能为空");
        }
        
        if (templateName == null || templateName.trim().isEmpty()) {
            throw new IllegalArgumentException("模板名称不能为空");
        }
        
        if (templateType == null || templateType.trim().isEmpty()) {
            throw new IllegalArgumentException("模板类型不能为空");
        }
        
        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || 
            (!originalFilename.toLowerCase().endsWith(".xlsx") && 
             !originalFilename.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("文件格式不支持，请上传 .xlsx 或 .xls 格式的 Excel 文件");
        }
        
        // 验证文件大小（限制为 10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过 10MB");
        }
    }
    
    /**
     * 解析 Excel 文件
     * 
     * @param file Excel 文件
     * @return 检查项列表
     * @throws IOException IO异常
     */
    private List<ChecklistItem> parseExcelFile(MultipartFile file) throws IOException {
        List<ChecklistItem> items = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = createWorkbook(file, inputStream);
            
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new IllegalArgumentException("Excel 文件中没有找到工作表");
            }
            
            // 解析工作表数据
            items = parseSheet(sheet);
            
            workbook.close();
        }
        
        if (items.isEmpty()) {
            throw new IllegalArgumentException("Excel 文件中没有找到有效的检查项数据");
        }
        
        return items;
    }
    
    /**
     * 创建工作簿对象
     * 
     * @param file Excel 文件
     * @param inputStream 输入流
     * @return 工作簿对象
     * @throws IOException IO异常
     */
    private Workbook createWorkbook(MultipartFile file, InputStream inputStream) throws IOException {
        String filename = file.getOriginalFilename();
        if (filename != null && filename.toLowerCase().endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else {
            return new HSSFWorkbook(inputStream);
        }
    }
    
    /**
     * 解析工作表数据
     * 
     * @param sheet 工作表
     * @return 检查项列表
     */
    private List<ChecklistItem> parseSheet(Sheet sheet) {
        List<ChecklistItem> items = new ArrayList<>();
        
        // 查找表头行
        int headerRowIndex = findHeaderRow(sheet);
        if (headerRowIndex == -1) {
            throw new IllegalArgumentException("Excel 文件格式不正确，未找到表头行");
        }
        
        // 获取列索引映射
        ColumnMapping columnMapping = getColumnMapping(sheet.getRow(headerRowIndex));
        
        // 解析数据行
        for (int rowIndex = headerRowIndex + 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue;
            }
            
            try {
                ChecklistItem item = parseRowToItem(row, columnMapping, rowIndex + 1);
                if (item != null) {
                    items.add(item);
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("第 " + (rowIndex + 1) + " 行数据解析失败: " + e.getMessage());
            }
        }
        
        return items;
    }
    
    /**
     * 查找表头行
     * 
     * @param sheet 工作表
     * @return 表头行索引，-1 表示未找到
     */
    private int findHeaderRow(Sheet sheet) {
        for (int rowIndex = 0; rowIndex <= Math.min(5, sheet.getLastRowNum()); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null && isHeaderRow(row)) {
                return rowIndex;
            }
        }
        return -1;
    }
    
    /**
     * 判断是否为表头行
     * 
     * @param row 行对象
     * @return 是否为表头行
     */
    private boolean isHeaderRow(Row row) {
        // 检查是否包含必要的列标题
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue().trim().toLowerCase();
                if (cellValue.contains("序号") || cellValue.contains("编号") || 
                    cellValue.contains("内容") || cellValue.contains("检查项") ||
                    cellValue.contains("sequence") || cellValue.contains("content")) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 获取列映射关系
     * 
     * @param headerRow 表头行
     * @return 列映射对象
     */
    private ColumnMapping getColumnMapping(Row headerRow) {
        ColumnMapping mapping = new ColumnMapping();
        
        for (Cell cell : headerRow) {
            if (cell != null && cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue().trim().toLowerCase();
                int columnIndex = cell.getColumnIndex();
                
                if (cellValue.contains("序号") || cellValue.contains("编号") || cellValue.contains("sequence")) {
                    mapping.sequenceColumn = columnIndex;
                } else if (cellValue.contains("内容") || cellValue.contains("检查项") || cellValue.contains("content")) {
                    mapping.contentColumn = columnIndex;
                } else if (cellValue.contains("分类") || cellValue.contains("类别") || cellValue.contains("category")) {
                    mapping.categoryColumn = columnIndex;
                } else if (cellValue.contains("必填") || cellValue.contains("必须") || cellValue.contains("required")) {
                    mapping.requiredColumn = columnIndex;
                }
            }
        }
        
        // 验证必要的列是否存在
        if (mapping.contentColumn == -1) {
            throw new IllegalArgumentException("Excel 文件中未找到内容列，请确保表头包含'内容'或'检查项'列");
        }
        
        return mapping;
    }
    
    /**
     * 将行数据解析为检查项
     * 
     * @param row 数据行
     * @param mapping 列映射
     * @param rowNumber 行号（用于错误提示）
     * @return 检查项对象
     */
    private ChecklistItem parseRowToItem(Row row, ColumnMapping mapping, int rowNumber) {
        // 获取内容
        String content = getCellStringValue(row.getCell(mapping.contentColumn));
        if (content == null || content.trim().isEmpty()) {
            return null; // 跳过空内容行
        }
        
        ChecklistItem item = new ChecklistItem();
        item.setId("item_" + UUID.randomUUID().toString().replace("-", ""));
        item.setContent(content.trim());
        
        // 获取序号
        if (mapping.sequenceColumn != -1) {
            Integer sequence = getCellIntegerValue(row.getCell(mapping.sequenceColumn));
            if (sequence != null && sequence > 0) {
                item.setSequence(sequence);
            } else {
                item.setSequence(rowNumber - 1); // 使用行号作为默认序号
            }
        } else {
            item.setSequence(rowNumber - 1); // 使用行号作为默认序号
        }
        
        // 获取分类
        if (mapping.categoryColumn != -1) {
            String category = getCellStringValue(row.getCell(mapping.categoryColumn));
            if (category != null && !category.trim().isEmpty()) {
                item.setCategory(category.trim());
            }
        }
        
        // 获取是否必填
        if (mapping.requiredColumn != -1) {
            Boolean required = getCellBooleanValue(row.getCell(mapping.requiredColumn));
            item.setRequired(required != null ? required : false);
        } else {
            item.setRequired(false);
        }
        
        return item;
    }
    
    /**
     * 获取单元格字符串值
     * 
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格整数值
     * 
     * @param cell 单元格
     * @return 整数值
     */
    private Integer getCellIntegerValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (int) cell.getNumericCellValue();
            case STRING:
                try {
                    return Integer.parseInt(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格布尔值
     * 
     * @param cell 单元格
     * @return 布尔值
     */
    private Boolean getCellBooleanValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case STRING:
                String stringValue = cell.getStringCellValue().trim().toLowerCase();
                if ("true".equals(stringValue) || "是".equals(stringValue) || "1".equals(stringValue)) {
                    return true;
                } else if ("false".equals(stringValue) || "否".equals(stringValue) || "0".equals(stringValue)) {
                    return false;
                } else {
                    return null;
                }
            case NUMERIC:
                return cell.getNumericCellValue() != 0;
            default:
                return null;
        }
    }
    
    /**
     * 判断是否为空行
     * 
     * @param row 行对象
     * @return 是否为空行
     */
    private boolean isEmptyRow(Row row) {
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String cellValue = getCellStringValue(cell);
                if (cellValue != null && !cellValue.trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 列映射类
     */
    private static class ColumnMapping {
        int sequenceColumn = -1;    // 序号列
        int contentColumn = -1;     // 内容列
        int categoryColumn = -1;    // 分类列
        int requiredColumn = -1;    // 必填列
    }
}