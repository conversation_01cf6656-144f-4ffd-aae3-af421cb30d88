{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "start": "npm run dev", "serve": "npm run preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.11.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}