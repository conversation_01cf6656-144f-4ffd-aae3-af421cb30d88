import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// Loading instance for global loading state
let loadingInstance: any = null
let requestCount = 0

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errorCode?: string
  timestamp?: string
  path?: string
}

// Request configuration interface
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
  successMessage?: string
}

// Create axios instance
const request = axios.create({
  baseURL: '/api', // Base URL for API requests
  timeout: 30000, // Request timeout (30 seconds)
  headers: {
    'Content-Type': 'application/json',
  },
})

// Show loading
const showLoading = () => {
  if (requestCount === 0) {
    loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
  }
  requestCount++
}

// Hide loading
const hideLoading = () => {
  requestCount--
  if (requestCount <= 0) {
    requestCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

// Request interceptor
request.interceptors.request.use(
  (config: any) => {
    // Show loading if enabled (default: true)
    if (config.showLoading !== false) {
      showLoading()
    }

    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }

    // Add authentication token if needed
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add request ID for tracking
    config.headers = config.headers || {}
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Log request in development
    if (import.meta.env.DEV) {
      console.log('🚀 API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        params: config.params,
        data: config.data,
        headers: config.headers,
      })
    }

    return config
  },
  (error) => {
    hideLoading()
    console.error('❌ Request interceptor error:', error)
    ElMessage.error('请求配置错误')
    return Promise.reject(error)
  },
)

// Response interceptor
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    hideLoading()
    
    const config = response.config as RequestConfig
    const { data } = response

    // Log response in development
    if (import.meta.env.DEV) {
      console.log('✅ API Response:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        status: response.status,
        data: data,
      })
    }

    // Handle successful response
    if (data.success !== false) {
      // Show success message if configured
      if (config.showSuccess && config.successMessage) {
        ElMessage.success(config.successMessage)
      }
      
      // Return data directly for easier consumption
      return data.data !== undefined ? data.data : data
    } else {
      // Handle business error
      const errorMessage = data.message || '请求失败'
      if (config.showError !== false) {
        ElMessage.error(errorMessage)
      }
      return Promise.reject(new Error(errorMessage))
    }
  },
  (error) => {
    hideLoading()
    
    const config = error.config as RequestConfig
    
    // Log error in development
    if (import.meta.env.DEV) {
      console.error('❌ API Error:', {
        method: config?.method?.toUpperCase(),
        url: config?.url,
        status: error.response?.status,
        message: error.message,
        response: error.response?.data,
      })
    }

    // Handle HTTP errors
    let message = '网络错误，请稍后重试'
    
    if (error.response) {
      const { status, data } = error.response
      
      // Try to get error message from response
      if (data && typeof data === 'object') {
        message = data.message || data.error || message
      }
      
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          break
        case 401:
          message = '未授权访问，请重新登录'
          // Clear token and redirect to login if needed
          localStorage.removeItem('auth_token')
          break
        case 403:
          message = '权限不足，无法访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 422:
          message = data?.message || '数据验证失败'
          break
        case 429:
          message = '请求过于频繁，请稍后重试'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务暂时不可用'
          break
        case 504:
          message = '请求超时'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时，请检查网络连接'
    } else if (error.message === 'Network Error') {
      message = '网络连接失败，请检查网络设置'
    }

    // Show error message if not disabled
    if (config?.showError !== false) {
      ElMessage.error(message)
    }

    // Enhance error object with more information
    const enhancedError = new Error(message)
    ;(enhancedError as any).status = error.response?.status
    ;(enhancedError as any).code = error.code
    ;(enhancedError as any).config = config
    ;(enhancedError as any).response = error.response

    return Promise.reject(enhancedError)
  },
)

// Utility functions for common request patterns
export const get = <T = any>(url: string, config?: RequestConfig): Promise<T> => {
  return request.get(url, config)
}

export const post = <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
  return request.post(url, data, config)
}

export const put = <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
  return request.put(url, data, config)
}

export const del = <T = any>(url: string, config?: RequestConfig): Promise<T> => {
  return request.delete(url, config)
}

export const patch = <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
  return request.patch(url, data, config)
}

export default request
