<template>
  <el-breadcrumb class="breadcrumb" separator="/">
    <el-breadcrumb-item 
      v-for="(item, index) in breadcrumbItems" 
      :key="index"
      :to="item.to && item.to !== '' ? item.to : undefined"
    >
      {{ item.text }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

interface BreadcrumbItem {
  text: string
  to?: string
}

const route = useRoute()

const breadcrumbItems = computed(() => {
  const items = route.meta?.breadcrumb as BreadcrumbItem[] || []
  
  // If we're on a dynamic route, update the breadcrumb text
  if (route.name === 'ChecklistReview' && route.params.type) {
    const lastItem = items[items.length - 1]
    if (lastItem) {
      lastItem.text = `${route.params.type} 评审`
    }
  }
  
  if (route.name === 'ReviewHistory' && route.params.type) {
    const lastItem = items[items.length - 1]
    if (lastItem) {
      lastItem.text = `${route.params.type} 评审历史`
    }
  }
  
  return items
})
</script>

<style scoped>
.breadcrumb {
  margin-bottom: 20px;
  padding: 10px 0;
}
</style>