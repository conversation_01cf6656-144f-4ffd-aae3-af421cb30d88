<template>
  <div class="app-navigation">
    <el-menu
      :default-active="activeIndex"
      mode="horizontal"
      @select="handleSelect"
      router
    >
      <el-menu-item index="/review">
        <el-icon><View /></el-icon>
        <span>评审系统</span>
      </el-menu-item>
      <el-menu-item index="/admin">
        <el-icon><Setting /></el-icon>
        <span>后台管理</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { View, Setting } from '@element-plus/icons-vue'

const route = useRoute()

const activeIndex = computed(() => {
  const path = route.path
  if (path.startsWith('/admin')) {
    return '/admin'
  } else if (path.startsWith('/review')) {
    return '/review'
  }
  return '/review'
})

const handleSelect = (key: string) => {
  // Navigation is handled by router prop
}
</script>

<style scoped>
.app-navigation {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
}

.el-menu--horizontal {
  border-bottom: none;
}
</style>