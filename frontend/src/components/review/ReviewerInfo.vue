<template>
  <div class="reviewer-info">
    <!-- Summary statistics -->
    <div class="summary-section">
      <h3>评审概览</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-card">
            <el-statistic
              title="总评审人数"
              :value="reviewerStats.totalReviewers"
              suffix="人"
            />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <el-statistic
              title="总操作次数"
              :value="reviewerStats.totalActions"
              suffix="次"
            />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <el-statistic
              title="冲突记录"
              :value="reviewerStats.conflicts"
              suffix="个"
            />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <el-statistic
              title="最后更新"
              :value="formatRelativeTime(reviewerStats.lastUpdateTime)"
            />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Reviewer list -->
    <div class="reviewers-section">
      <h3>参与评审人员</h3>
      <div class="reviewers-grid">
        <el-card
          v-for="reviewer in reviewerList"
          :key="reviewer.name"
          class="reviewer-card"
          :class="{ 'has-conflicts': reviewer.hasConflicts }"
        >
          <div class="reviewer-header">
            <div class="reviewer-avatar">
              <el-avatar :size="40">
                {{ reviewer.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="reviewer-info-text">
              <h4>{{ reviewer.name }}</h4>
              <p class="reviewer-role">{{ reviewer.role || '评审员' }}</p>
            </div>
            <div class="reviewer-status">
              <el-tag
                :type="getReviewerStatusType(reviewer.status)"
                size="small"
              >
                {{ getReviewerStatusText(reviewer.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="reviewer-stats">
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="stat-item">
                  <span class="stat-value">{{ reviewer.completedItems }}</span>
                  <span class="stat-label">已评审</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <span class="stat-value">{{ reviewer.passedItems }}</span>
                  <span class="stat-label">通过</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <span class="stat-value">{{ reviewer.failedItems }}</span>
                  <span class="stat-label">不通过</span>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <div class="reviewer-time">
            <el-icon><Clock /></el-icon>
            <span>最后活动: {{ formatRelativeTime(reviewer.lastActiveTime) }}</span>
          </div>
          
          <div v-if="reviewer.hasConflicts" class="conflict-indicator">
            <el-icon class="conflict-icon"><Warning /></el-icon>
            <span>存在 {{ reviewer.conflictCount }} 个冲突</span>
          </div>
        </el-card>
      </div>
    </div>

    <!-- Timeline section -->
    <div class="timeline-section">
      <div class="timeline-header">
        <h3>评审时间线</h3>
        <div class="timeline-filters">
          <el-select
            v-model="timelineFilter.reviewer"
            placeholder="筛选评审人员"
            clearable
            size="small"
          >
            <el-option
              v-for="reviewer in reviewerList"
              :key="reviewer.name"
              :label="reviewer.name"
              :value="reviewer.name"
            />
          </el-select>
          
          <el-select
            v-model="timelineFilter.action"
            placeholder="筛选操作类型"
            clearable
            size="small"
          >
            <el-option label="状态更新" value="status_update" />
            <el-option label="添加备注" value="add_comment" />
            <el-option label="批量操作" value="batch_operation" />
            <el-option label="完成评审" value="complete_review" />
          </el-select>
          
          <el-button
            type="primary"
            size="small"
            @click="filterTimeline"
          >
            筛选
          </el-button>
        </div>
      </div>
      
      <div class="timeline-content">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in filteredTimeline"
            :key="index"
            :timestamp="formatDateTime(item.reviewTime)"
            :type="getTimelineItemType(item)"
            :icon="getTimelineIcon(item)"
            placement="top"
          >
            <div class="timeline-item-content">
              <div class="timeline-header">
                <span class="reviewer-name">{{ item.reviewer }}</span>
                <span class="action-type">{{ getActionText(item.action) }}</span>
                <el-tag
                  v-if="item.status"
                  :type="getStatusTagType(item.status)"
                  size="small"
                >
                  {{ getStatusText(item.status) }}
                </el-tag>
              </div>
              
              <div v-if="item.comment" class="timeline-comment">
                <el-icon><ChatDotRound /></el-icon>
                <span>{{ item.comment }}</span>
              </div>
              
              <div v-if="item.action === 'batch_operation'" class="batch-info">
                <el-icon><Operation /></el-icon>
                <span>批量操作</span>
              </div>
              
              <div v-if="isConflictItem(item)" class="conflict-warning">
                <el-icon class="conflict-icon"><Warning /></el-icon>
                <span>此操作与其他评审人员的操作存在冲突</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        
        <div v-if="filteredTimeline.length === 0" class="empty-timeline">
          <el-empty description="暂无评审记录" />
        </div>
      </div>
    </div>

    <!-- Conflict resolution section -->
    <div v-if="conflictItems.length > 0" class="conflicts-section">
      <h3>冲突处理</h3>
      <el-alert
        title="检测到评审冲突"
        type="warning"
        :description="`发现 ${conflictItems.length} 个检查项存在不同评审人员的冲突结果，请及时处理`"
        show-icon
        :closable="false"
      />
      
      <div class="conflicts-list">
        <el-card
          v-for="conflict in conflictItems"
          :key="conflict.itemId"
          class="conflict-card"
        >
          <div class="conflict-header">
            <h4>检查项 #{{ conflict.sequence }}: {{ conflict.content }}</h4>
            <el-tag type="danger" size="small">冲突</el-tag>
          </div>
          
          <div class="conflict-details">
            <div class="conflict-reviews">
              <div
                v-for="review in conflict.conflictingReviews"
                :key="`${review.reviewer}-${review.reviewTime}`"
                class="conflict-review-item"
              >
                <div class="review-header">
                  <el-avatar :size="24">{{ review.reviewer.charAt(0) }}</el-avatar>
                  <span class="reviewer-name">{{ review.reviewer }}</span>
                  <el-tag
                    :type="getStatusTagType(review.status)"
                    size="small"
                  >
                    {{ getStatusText(review.status) }}
                  </el-tag>
                  <span class="review-time">{{ formatDateTime(review.reviewTime) }}</span>
                </div>
                <div v-if="review.comment" class="review-comment">
                  {{ review.comment }}
                </div>
              </div>
            </div>
            
            <div class="conflict-actions">
              <el-button
                type="primary"
                size="small"
                @click="resolveConflict(conflict, 'manual')"
              >
                手动解决
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="resolveConflict(conflict, 'latest')"
              >
                采用最新结果
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="resolveConflict(conflict, 'discussion')"
              >
                标记讨论
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock,
  Warning,
  ChatDotRound,
  Operation,
  User,
  Check,
  Close,
  Edit
} from '@element-plus/icons-vue'
import type { ReviewHistory, ReviewItemStatus } from '@/api/review'
import { ReviewItemStatus as ItemStatus } from '@/api/review'

// Component props
interface Props {
  reviewId: string
  reviewHistory: ReviewHistory[]
}

const props = defineProps<Props>()

// Interfaces
interface ReviewerSummary {
  name: string
  role?: string
  status: 'active' | 'inactive' | 'completed'
  completedItems: number
  passedItems: number
  failedItems: number
  skippedItems: number
  lastActiveTime: string
  hasConflicts: boolean
  conflictCount: number
}

interface ConflictItem {
  itemId: string
  sequence: number
  content: string
  conflictingReviews: ReviewHistory[]
}

interface TimelineFilter {
  reviewer: string
  action: string
}

// Reactive data
const reviewerList = ref<ReviewerSummary[]>([])
const conflictItems = ref<ConflictItem[]>([])
const timelineFilter = reactive<TimelineFilter>({
  reviewer: '',
  action: ''
})

// Computed properties
const reviewerStats = computed(() => {
  const totalReviewers = reviewerList.value.length
  const totalActions = props.reviewHistory.length
  const conflicts = conflictItems.value.length
  const lastUpdateTime = props.reviewHistory.length > 0 
    ? props.reviewHistory[0].reviewTime 
    : ''
  
  return {
    totalReviewers,
    totalActions,
    conflicts,
    lastUpdateTime
  }
})

const filteredTimeline = computed(() => {
  let timeline = [...props.reviewHistory]
  
  if (timelineFilter.reviewer) {
    timeline = timeline.filter(item => item.reviewer === timelineFilter.reviewer)
  }
  
  if (timelineFilter.action) {
    timeline = timeline.filter(item => item.action === timelineFilter.action)
  }
  
  return timeline.sort((a, b) => 
    new Date(b.reviewTime).getTime() - new Date(a.reviewTime).getTime()
  )
})

// Methods
const processReviewHistory = () => {
  // Process reviewer summaries
  const reviewerMap = new Map<string, ReviewerSummary>()
  
  props.reviewHistory.forEach(history => {
    if (!reviewerMap.has(history.reviewer)) {
      reviewerMap.set(history.reviewer, {
        name: history.reviewer,
        status: 'active',
        completedItems: 0,
        passedItems: 0,
        failedItems: 0,
        skippedItems: 0,
        lastActiveTime: history.reviewTime,
        hasConflicts: false,
        conflictCount: 0
      })
    }
    
    const reviewer = reviewerMap.get(history.reviewer)!
    
    // Update statistics
    if (history.action === 'status_update') {
      reviewer.completedItems++
      
      switch (history.status) {
        case ItemStatus.PASS:
          reviewer.passedItems++
          break
        case ItemStatus.FAIL:
          reviewer.failedItems++
          break
        case ItemStatus.SKIP:
          reviewer.skippedItems++
          break
      }
    }
    
    // Update last active time
    if (new Date(history.reviewTime) > new Date(reviewer.lastActiveTime)) {
      reviewer.lastActiveTime = history.reviewTime
    }
  })
  
  reviewerList.value = Array.from(reviewerMap.values())
  
  // Process conflicts
  processConflicts()
}

const processConflicts = () => {
  const itemMap = new Map<string, ReviewHistory[]>()
  
  // Group history by item ID
  props.reviewHistory.forEach(history => {
    if (history.action === 'status_update') {
      if (!itemMap.has(history.id)) {
        itemMap.set(history.id, [])
      }
      itemMap.get(history.id)!.push(history)
    }
  })
  
  // Find conflicts
  const conflicts: ConflictItem[] = []
  
  itemMap.forEach((histories, itemId) => {
    if (histories.length > 1) {
      // Check if there are different statuses from different reviewers
      const statusMap = new Map<string, ReviewHistory[]>()
      
      histories.forEach(history => {
        const key = `${history.reviewer}-${history.status}`
        if (!statusMap.has(key)) {
          statusMap.set(key, [])
        }
        statusMap.get(key)!.push(history)
      })
      
      // Check for actual conflicts (different statuses from different reviewers)
      const reviewerStatuses = new Map<string, string>()
      let hasConflict = false
      
      histories.forEach(history => {
        const existingStatus = reviewerStatuses.get(history.reviewer)
        if (existingStatus && existingStatus !== history.status) {
          hasConflict = true
        } else if (!existingStatus) {
          reviewerStatuses.set(history.reviewer, history.status)
        }
      })
      
      // Check for conflicts between different reviewers
      const uniqueStatuses = new Set(Array.from(reviewerStatuses.values()))
      if (uniqueStatuses.size > 1) {
        hasConflict = true
      }
      
      if (hasConflict) {
        conflicts.push({
          itemId,
          sequence: parseInt(itemId.split('-').pop() || '0'),
          content: `检查项 ${itemId}`, // This should come from the actual item data
          conflictingReviews: histories
        })
        
        // Update reviewer conflict counts
        const involvedReviewers = new Set(histories.map(h => h.reviewer))
        involvedReviewers.forEach(reviewerName => {
          const reviewer = reviewerList.value.find(r => r.name === reviewerName)
          if (reviewer) {
            reviewer.hasConflicts = true
            reviewer.conflictCount++
          }
        })
      }
    }
  })
  
  conflictItems.value = conflicts
}

const filterTimeline = () => {
  // The filtering is handled by the computed property
  // This method can be used for additional processing if needed
}

const resolveConflict = async (conflict: ConflictItem, resolution: 'manual' | 'latest' | 'discussion') => {
  try {
    switch (resolution) {
      case 'manual':
        await handleManualResolution(conflict)
        break
      case 'latest':
        await handleLatestResolution(conflict)
        break
      case 'discussion':
        await handleDiscussionResolution(conflict)
        break
    }
    
    // Refresh data after resolution
    processReviewHistory()
    ElMessage.success('冲突处理成功')
  } catch (error) {
    console.error('Failed to resolve conflict:', error)
    ElMessage.error('冲突处理失败')
  }
}

const handleManualResolution = async (conflict: ConflictItem) => {
  const { value: resolution } = await ElMessageBox.prompt(
    `请为检查项 "${conflict.content}" 选择最终状态`,
    '手动解决冲突',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入解决方案和最终状态...'
    }
  )
  
  // Here you would typically call an API to resolve the conflict
  console.log('Manual resolution:', resolution)
}

const handleLatestResolution = async (conflict: ConflictItem) => {
  await ElMessageBox.confirm(
    `确定采用最新的评审结果来解决冲突吗？`,
    '确认操作',
    { type: 'warning' }
  )
  
  // Here you would typically call an API to resolve the conflict with latest result
  console.log('Latest resolution for:', conflict.itemId)
}

const handleDiscussionResolution = async (conflict: ConflictItem) => {
  const { value: note } = await ElMessageBox.prompt(
    `请添加讨论备注`,
    '标记讨论',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入需要讨论的内容...'
    }
  )
  
  // Here you would typically call an API to mark the conflict for discussion
  console.log('Discussion note:', note)
}

const isConflictItem = (item: ReviewHistory) => {
  return conflictItems.value.some(conflict => 
    conflict.conflictingReviews.some(review => 
      review.id === item.id && review.reviewer === item.reviewer
    )
  )
}

// Utility methods
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatRelativeTime = (dateTime: string) => {
  if (!dateTime) return '-'
  
  const now = new Date()
  const time = new Date(dateTime)
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return formatDateTime(dateTime)
}

const getReviewerStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    completed: 'primary'
  }
  return statusMap[status] || 'info'
}

const getReviewerStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    completed: '已完成'
  }
  return statusMap[status] || status
}

const getTimelineItemType = (item: ReviewHistory) => {
  if (isConflictItem(item)) return 'danger'
  
  switch (item.action) {
    case 'status_update':
      return item.status === ItemStatus.PASS ? 'success' : 
             item.status === ItemStatus.FAIL ? 'danger' : 'warning'
    case 'add_comment':
      return 'info'
    case 'batch_operation':
      return 'primary'
    default:
      return 'info'
  }
}

const getTimelineIcon = (item: ReviewHistory) => {
  switch (item.action) {
    case 'status_update':
      return item.status === ItemStatus.PASS ? Check : 
             item.status === ItemStatus.FAIL ? Close : Edit
    case 'add_comment':
      return ChatDotRound
    case 'batch_operation':
      return Operation
    default:
      return User
  }
}

const getActionText = (action: string) => {
  const actionMap: Record<string, string> = {
    status_update: '更新状态',
    add_comment: '添加备注',
    batch_operation: '批量操作',
    complete_review: '完成评审'
  }
  return actionMap[action] || action
}

const getStatusTagType = (status: ReviewItemStatus) => {
  const statusMap: Record<ReviewItemStatus, string> = {
    [ItemStatus.PENDING]: 'info',
    [ItemStatus.PASS]: 'success',
    [ItemStatus.FAIL]: 'danger',
    [ItemStatus.SKIP]: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: ReviewItemStatus) => {
  const statusMap: Record<ReviewItemStatus, string> = {
    [ItemStatus.PENDING]: '待处理',
    [ItemStatus.PASS]: '通过',
    [ItemStatus.FAIL]: '不通过',
    [ItemStatus.SKIP]: '跳过'
  }
  return statusMap[status] || status
}

// Watch for changes in review history
watch(() => props.reviewHistory, () => {
  processReviewHistory()
}, { immediate: true })

// Lifecycle
onMounted(() => {
  processReviewHistory()
})
</script>

<style scoped>
.reviewer-info {
  padding: 20px;
}

.summary-section,
.reviewers-section,
.timeline-section,
.conflicts-section {
  margin-bottom: 32px;
}

.summary-section h3,
.reviewers-section h3,
.timeline-section h3,
.conflicts-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.summary-card {
  text-align: center;
}

.reviewers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.reviewer-card {
  transition: all 0.3s ease;
}

.reviewer-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reviewer-card.has-conflicts {
  border-left: 4px solid #f56c6c;
}

.reviewer-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.reviewer-avatar {
  margin-right: 12px;
}

.reviewer-info-text {
  flex: 1;
}

.reviewer-info-text h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.reviewer-role {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.reviewer-stats {
  margin-bottom: 12px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.reviewer-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
  margin-bottom: 8px;
}

.conflict-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
}

.conflict-icon {
  color: #f56c6c;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.timeline-filters {
  display: flex;
  gap: 8px;
  align-items: center;
}

.timeline-content {
  max-height: 500px;
  overflow-y: auto;
}

.timeline-item-content {
  padding: 8px 0;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reviewer-name {
  font-weight: 600;
  color: #303133;
}

.action-type {
  color: #606266;
  font-size: 14px;
}

.timeline-comment {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #606266;
  font-size: 14px;
  margin-top: 4px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 14px;
  margin-top: 4px;
}

.conflict-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 14px;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #fef0f0;
  border-radius: 4px;
}

.empty-timeline {
  text-align: center;
  padding: 40px 0;
}

.conflicts-list {
  margin-top: 16px;
}

.conflict-card {
  margin-bottom: 16px;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.conflict-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.conflict-reviews {
  margin-bottom: 16px;
}

.conflict-review-item {
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.review-comment {
  color: #606266;
  font-size: 14px;
  padding-left: 32px;
}

.review-time {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
}

.conflict-actions {
  display: flex;
  gap: 8px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>