<template>
  <div class="dev-tools" v-if="isDev">
    <el-card class="dev-tools-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🛠️ 开发者工具</span>
          <el-button 
            type="text" 
            @click="collapsed = !collapsed"
            :icon="collapsed ? 'ArrowDown' : 'ArrowUp'"
          />
        </div>
      </template>
      
      <div v-show="!collapsed" class="dev-tools-content">
        <!-- 环境信息 -->
        <div class="section">
          <h4>环境信息</h4>
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="模式">{{ envInfo.mode }}</el-descriptions-item>
            <el-descriptions-item label="API基础路径">{{ envInfo.apiBaseUrl }}</el-descriptions-item>
            <el-descriptions-item label="后端主机">{{ envInfo.backendHost }}</el-descriptions-item>
            <el-descriptions-item label="后端路径">{{ envInfo.backendPath }}</el-descriptions-item>
            <el-descriptions-item label="开发端口">{{ envInfo.devPort }}</el-descriptions-item>
            <el-descriptions-item label="调试模式">{{ envInfo.debug ? '开启' : '关闭' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 连接测试 -->
        <div class="section">
          <h4>连接测试</h4>
          <div class="test-buttons">
            <el-button 
              type="primary" 
              @click="testConnection"
              :loading="testing"
              icon="Connection"
            >
              测试后端连接
            </el-button>
            <el-button 
              type="success" 
              @click="runFullTest"
              :loading="testing"
              icon="Check"
            >
              运行完整测试
            </el-button>
          </div>
        </div>

        <!-- 测试结果 -->
        <div class="section" v-if="testResults.length > 0">
          <h4>测试结果</h4>
          <div class="test-results">
            <div 
              v-for="(result, index) in testResults" 
              :key="index"
              class="test-result"
              :class="{ success: result.success, error: !result.success }"
            >
              <div class="result-header">
                <el-icon :class="result.success ? 'success-icon' : 'error-icon'">
                  <Check v-if="result.success" />
                  <Close v-else />
                </el-icon>
                <span class="result-message">{{ result.message }}</span>
              </div>
              <div class="result-details" v-if="result.details">
                <span>状态: {{ result.details.status }}</span>
                <span>响应时间: {{ result.details.responseTime }}ms</span>
                <span>端点: {{ result.details.endpoint }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  testBackendConnection, 
  runConnectionTestSuite, 
  getEnvironmentInfo,
  logEnvironmentInfo,
  type ConnectionTestResult 
} from '@/utils/connection-test'
import { Check, Close } from '@element-plus/icons-vue'

// 响应式数据
const collapsed = ref(false)
const testing = ref(false)
const testResults = ref<ConnectionTestResult[]>([])

// 计算属性
const isDev = computed(() => import.meta.env.DEV)
const envInfo = computed(() => getEnvironmentInfo())

// 方法
const testConnection = async () => {
  testing.value = true
  try {
    const result = await testBackendConnection()
    testResults.value = [result]
  } finally {
    testing.value = false
  }
}

const runFullTest = async () => {
  testing.value = true
  try {
    const results = await runConnectionTestSuite()
    testResults.value = results
  } finally {
    testing.value = false
  }
}

// 生命周期
onMounted(() => {
  logEnvironmentInfo()
})
</script>

<style scoped>
.dev-tools {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
}

.dev-tools-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dev-tools-content {
  max-height: 60vh;
  overflow-y: auto;
}

.section {
  margin-bottom: 16px;
}

.section h4 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 14px;
}

.test-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-result {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.test-result.success {
  background: #f0f9ff;
  border-color: #67c23a;
}

.test-result.error {
  background: #fef0f0;
  border-color: #f56c6c;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.result-message {
  font-weight: 500;
  font-size: 13px;
}

.result-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
}

.result-details span {
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 2px;
}
</style>
