<template>
  <div class="admin-dashboard">
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <h1>Checklist Review System - 后台管理</h1>
        </div>
        <div class="header-right">
          <el-button type="success" @click="$router.push('/review')">
            <el-icon><View /></el-icon>
            前台评审
          </el-button>
        </div>
      </el-header>

      <el-container>
        <el-aside width="200px" class="sidebar">
          <el-menu 
            :default-active="activeMenu" 
            class="sidebar-menu" 
            @select="handleMenuSelect"
            router
          >
            <el-menu-item index="templates">
              <el-icon><Document /></el-icon>
              <span>模板管理</span>
            </el-menu-item>
            <el-menu-item index="import">
              <el-icon><Upload /></el-icon>
              <span>Excel导入</span>
            </el-menu-item>
            <el-menu-item index="config">
              <el-icon><Setting /></el-icon>
              <span>检查项配置</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <el-main class="main-content">
          <Breadcrumb />
          
          <div v-if="activeMenu === 'templates'" class="content-section">
            <div class="section-header">
              <h2>模板管理</h2>
              <p>管理检查单模板，创建和编辑不同类型的评审模板</p>
            </div>
            <TemplateList />
          </div>

          <div v-if="activeMenu === 'import'" class="content-section">
            <div class="section-header">
              <h2>Excel导入</h2>
              <p>从Excel文件批量导入检查项，快速创建模板</p>
            </div>
            <ExcelImport @go-to-templates="handleGoToTemplates" />
          </div>

          <div v-if="activeMenu === 'config'" class="content-section">
            <div class="section-header">
              <h2>检查项配置</h2>
              <p>配置检查项的详细信息，包括分类、标签等</p>
            </div>
            <ItemConfig />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Document, Upload, Setting, View } from '@element-plus/icons-vue'
import TemplateList from '@/components/admin/TemplateList.vue'
import ExcelImport from '@/components/admin/ExcelImport.vue'
import ItemConfig from '@/components/admin/ItemConfig.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'

const route = useRoute()
const router = useRouter()
const activeMenu = ref('templates')

const handleGoToTemplates = () => {
  activeMenu.value = 'templates'
  updatePageTitle('模板管理')
}

const handleMenuSelect = (key: string) => {
  activeMenu.value = key
  
  // Update page title based on selected menu
  switch (key) {
    case 'templates':
      updatePageTitle('模板管理')
      break
    case 'import':
      updatePageTitle('Excel导入')
      break
    case 'config':
      updatePageTitle('检查项配置')
      break
  }
}

const updatePageTitle = (subtitle: string) => {
  document.title = `${subtitle} - 后台管理 - Checklist Review System`
}

// Initialize active menu based on current route
onMounted(() => {
  // Set default active menu
  activeMenu.value = 'templates'
  updatePageTitle('模板管理')
})

// Watch for route changes to update active menu
watch(() => route.path, (newPath) => {
  if (newPath.includes('/admin')) {
    // Keep current menu selection when navigating within admin
  }
})
</script>

<style scoped>
.admin-dashboard {
  height: 100vh;
}

.header {
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sidebar {
  background-color: #f5f5f5;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border-right: none;
}

.main-content {
  padding: 20px;
  background-color: #fff;
}

.section-header {
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.section-header h2 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.section-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.content-section {
  background-color: #fff;
}
</style>
