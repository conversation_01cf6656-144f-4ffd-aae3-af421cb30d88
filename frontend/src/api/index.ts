/**
 * API Services Index
 * Centralized export for all API services
 */

// Export template API
export * from './template'
export { default as templateApi } from './template'

// Export review API
export * from './review'
export { default as reviewApi } from './review'

// Export request utilities
export { default as request, get, post, put, del, patch } from '@/utils/request'
export type { RequestConfig, ApiResponse } from '@/utils/request'

// API base configuration
export const API_CONFIG = {
  BASE_URL: '/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const

// Common API response types
export interface PaginationQuery {
  page?: number
  size?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  size: number
  totalPages: number
}

export interface ApiError {
  success: false
  errorCode: string
  message: string
  timestamp: string
  path: string
  details?: any
}

// API status codes
export const API_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const

// Common error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '未授权访问，请重新登录',
  FORBIDDEN: '权限不足，无法访问',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '数据验证失败',
  CONFLICT_ERROR: '数据冲突，请刷新后重试',
  TOO_MANY_REQUESTS: '请求过于频繁，请稍后重试',
} as const