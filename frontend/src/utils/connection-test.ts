/**
 * 前后端连接测试工具
 * 用于验证前端与后端服务器的连接状态
 */

import { get } from './request'
import { ElMessage, ElNotification } from 'element-plus'

export interface ConnectionTestResult {
  success: boolean
  message: string
  details?: {
    status?: number
    responseTime?: number
    timestamp: string
    endpoint: string
  }
}

/**
 * 测试后端连接
 */
export async function testBackendConnection(): Promise<ConnectionTestResult> {
  const startTime = Date.now()
  const timestamp = new Date().toISOString()
  
  try {
    console.log('🔍 开始测试后端连接...')
    
    // 尝试访问后端健康检查端点
    const response = await get('/health', {
      showLoading: false,
      showError: false,
      timeout: 10000
    })
    
    const responseTime = Date.now() - startTime
    
    const result: ConnectionTestResult = {
      success: true,
      message: '后端连接成功',
      details: {
        status: 200,
        responseTime,
        timestamp,
        endpoint: '/health'
      }
    }
    
    console.log('✅ 后端连接测试成功:', result)
    return result
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    
    const result: ConnectionTestResult = {
      success: false,
      message: `后端连接失败: ${error.message}`,
      details: {
        status: error.status || 0,
        responseTime,
        timestamp,
        endpoint: '/health'
      }
    }
    
    console.error('❌ 后端连接测试失败:', result)
    return result
  }
}

/**
 * 测试API端点连接
 */
export async function testApiEndpoint(endpoint: string): Promise<ConnectionTestResult> {
  const startTime = Date.now()
  const timestamp = new Date().toISOString()
  
  try {
    console.log(`🔍 测试API端点: ${endpoint}`)
    
    const response = await get(endpoint, {
      showLoading: false,
      showError: false,
      timeout: 10000
    })
    
    const responseTime = Date.now() - startTime
    
    const result: ConnectionTestResult = {
      success: true,
      message: `API端点 ${endpoint} 连接成功`,
      details: {
        status: 200,
        responseTime,
        timestamp,
        endpoint
      }
    }
    
    console.log(`✅ API端点测试成功:`, result)
    return result
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    
    const result: ConnectionTestResult = {
      success: false,
      message: `API端点 ${endpoint} 连接失败: ${error.message}`,
      details: {
        status: error.status || 0,
        responseTime,
        timestamp,
        endpoint
      }
    }
    
    console.error(`❌ API端点测试失败:`, result)
    return result
  }
}

/**
 * 显示连接测试结果
 */
export function showConnectionTestResult(result: ConnectionTestResult) {
  if (result.success) {
    ElNotification({
      title: '连接测试成功',
      message: `${result.message}\n响应时间: ${result.details?.responseTime}ms`,
      type: 'success',
      duration: 3000
    })
  } else {
    ElNotification({
      title: '连接测试失败',
      message: result.message,
      type: 'error',
      duration: 5000
    })
  }
}

/**
 * 运行完整的连接测试套件
 */
export async function runConnectionTestSuite(): Promise<ConnectionTestResult[]> {
  console.log('🚀 开始运行连接测试套件...')
  
  const results: ConnectionTestResult[] = []
  
  // 测试后端基础连接
  const backendResult = await testBackendConnection()
  results.push(backendResult)
  
  // 如果后端连接成功，测试主要API端点
  if (backendResult.success) {
    const apiEndpoints = [
      '/templates',
      '/reviews'
    ]
    
    for (const endpoint of apiEndpoints) {
      const apiResult = await testApiEndpoint(endpoint)
      results.push(apiResult)
    }
  }
  
  // 显示测试结果摘要
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  if (successCount === totalCount) {
    ElMessage.success(`所有连接测试通过 (${successCount}/${totalCount})`)
  } else {
    ElMessage.warning(`部分连接测试失败 (${successCount}/${totalCount})`)
  }
  
  console.log('📊 连接测试套件完成:', results)
  return results
}

/**
 * 获取当前环境配置信息
 */
export function getEnvironmentInfo() {
  return {
    mode: import.meta.env.MODE,
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
    backendHost: import.meta.env.VITE_BACKEND_HOST,
    backendPath: import.meta.env.VITE_BACKEND_PATH,
    devPort: import.meta.env.VITE_DEV_PORT,
    debug: import.meta.env.VITE_DEBUG === 'true'
  }
}

/**
 * 在控制台显示环境配置信息
 */
export function logEnvironmentInfo() {
  const env = getEnvironmentInfo()
  console.log('🌍 当前环境配置:', env)
  return env
}
