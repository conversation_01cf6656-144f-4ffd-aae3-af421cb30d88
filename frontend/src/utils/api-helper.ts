/**
 * API Helper Utilities
 * Common utilities for API operations
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import type { RequestConfig } from './request'

// Loading state management
class LoadingManager {
  private loadingStates = new Map<string, boolean>()
  private callbacks = new Map<string, ((loading: boolean) => void)[]>()

  setLoading(key: string, loading: boolean) {
    this.loadingStates.set(key, loading)
    const callbacks = this.callbacks.get(key) || []
    callbacks.forEach(callback => callback(loading))
  }

  isLoading(key: string): boolean {
    return this.loadingStates.get(key) || false
  }

  subscribe(key: string, callback: (loading: boolean) => void) {
    const callbacks = this.callbacks.get(key) || []
    callbacks.push(callback)
    this.callbacks.set(key, callbacks)
    
    // Return unsubscribe function
    return () => {
      const currentCallbacks = this.callbacks.get(key) || []
      const index = currentCallbacks.indexOf(callback)
      if (index > -1) {
        currentCallbacks.splice(index, 1)
        this.callbacks.set(key, currentCallbacks)
      }
    }
  }

  clear(key?: string) {
    if (key) {
      this.loadingStates.delete(key)
      this.callbacks.delete(key)
    } else {
      this.loadingStates.clear()
      this.callbacks.clear()
    }
  }
}

export const loadingManager = new LoadingManager()

// API operation wrapper with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: {
    loadingKey?: string
    successMessage?: string
    errorMessage?: string
    showConfirm?: boolean
    confirmMessage?: string
  } = {}
): Promise<T | null> {
  const { loadingKey, successMessage, errorMessage, showConfirm, confirmMessage } = options

  try {
    // Show confirmation if required
    if (showConfirm) {
      await ElMessageBox.confirm(
        confirmMessage || '确定要执行此操作吗？',
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    }

    // Set loading state
    if (loadingKey) {
      loadingManager.setLoading(loadingKey, true)
    }

    // Execute operation
    const result = await operation()

    // Show success message
    if (successMessage) {
      ElMessage.success(successMessage)
    }

    return result
  } catch (error: any) {
    // Handle cancellation
    if (error === 'cancel') {
      return null
    }

    // Show error message
    const message = errorMessage || error.message || '操作失败'
    ElMessage.error(message)

    // Log error in development
    if (import.meta.env.DEV) {
      console.error('API operation failed:', error)
    }

    throw error
  } finally {
    // Clear loading state
    if (loadingKey) {
      loadingManager.setLoading(loadingKey, false)
    }
  }
}

// Retry mechanism for failed requests
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: {
    maxAttempts?: number
    delay?: number
    backoff?: boolean
    shouldRetry?: (error: any) => boolean
  } = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoff = true,
    shouldRetry = (error) => error.status >= 500 || error.code === 'ECONNABORTED'
  } = options

  let lastError: any
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error: any) {
      lastError = error
      
      // Don't retry if this is the last attempt or if we shouldn't retry this error
      if (attempt === maxAttempts || !shouldRetry(error)) {
        throw error
      }

      // Calculate delay with optional backoff
      const currentDelay = backoff ? delay * Math.pow(2, attempt - 1) : delay
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, currentDelay))
      
      console.warn(`API request failed, retrying (${attempt}/${maxAttempts})...`, error.message)
    }
  }
  
  throw lastError
}

// Debounced API calls
export function debounceApiCall<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  delay: number = 300
): T {
  let timeoutId: number | null = null
  let lastPromise: Promise<any> | null = null

  return ((...args: Parameters<T>) => {
    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // Return existing promise if still pending
    if (lastPromise) {
      return lastPromise
    }

    // Create new promise
    lastPromise = new Promise((resolve, reject) => {
      timeoutId = setTimeout(async () => {
        try {
          const result = await fn(...args)
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          lastPromise = null
          timeoutId = null
        }
      }, delay)
    })

    return lastPromise
  }) as T
}

// Batch API operations
export async function batchApiCalls<T>(
  operations: (() => Promise<T>)[],
  options: {
    concurrency?: number
    failFast?: boolean
    onProgress?: (completed: number, total: number) => void
  } = {}
): Promise<(T | Error)[]> {
  const { concurrency = 5, failFast = false, onProgress } = options
  const results: (T | Error)[] = []
  let completed = 0

  // Process operations in batches
  for (let i = 0; i < operations.length; i += concurrency) {
    const batch = operations.slice(i, i + concurrency)
    
    const batchPromises = batch.map(async (operation, index) => {
      try {
        const result = await operation()
        completed++
        onProgress?.(completed, operations.length)
        return result
      } catch (error: any) {
        completed++
        onProgress?.(completed, operations.length)
        
        if (failFast) {
          throw error
        }
        
        return error
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)

    // Stop if fail fast is enabled and we have errors
    if (failFast && batchResults.some(result => result instanceof Error)) {
      break
    }
  }

  return results
}

// File upload helper
export function createFileUploadConfig(
  onProgress?: (progress: number) => void
): RequestConfig {
  return {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 300000, // 5 minutes for file uploads
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    },
  }
}

// Download helper
export function downloadFile(blob: Blob, filename: string) {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// Notification helpers
export const notify = {
  success: (message: string, title = '成功') => {
    ElNotification.success({
      title,
      message,
      duration: 3000,
    })
  },
  
  error: (message: string, title = '错误') => {
    ElNotification.error({
      title,
      message,
      duration: 5000,
    })
  },
  
  warning: (message: string, title = '警告') => {
    ElNotification.warning({
      title,
      message,
      duration: 4000,
    })
  },
  
  info: (message: string, title = '提示') => {
    ElNotification.info({
      title,
      message,
      duration: 3000,
    })
  },
}

// Form validation helpers
export const validators = {
  required: (message = '此字段为必填项') => (value: any) => {
    if (value === null || value === undefined || value === '') {
      return message
    }
    return true
  },
  
  minLength: (min: number, message?: string) => (value: string) => {
    if (value && value.length < min) {
      return message || `最少需要 ${min} 个字符`
    }
    return true
  },
  
  maxLength: (max: number, message?: string) => (value: string) => {
    if (value && value.length > max) {
      return message || `最多允许 ${max} 个字符`
    }
    return true
  },
  
  email: (message = '请输入有效的邮箱地址') => (value: string) => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return message
    }
    return true
  },
  
  url: (message = '请输入有效的URL地址') => (value: string) => {
    if (value && !/^https?:\/\/.+/.test(value)) {
      return message
    }
    return true
  },
}

// Local storage helpers with error handling
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | undefined => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error(`Failed to get item from localStorage: ${key}`, error)
      return defaultValue
    }
  },
  
  set: (key: string, value: any): boolean => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error(`Failed to set item in localStorage: ${key}`, error)
      return false
    }
  },
  
  remove: (key: string): boolean => {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error(`Failed to remove item from localStorage: ${key}`, error)
      return false
    }
  },
  
  clear: (): boolean => {
    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.error('Failed to clear localStorage', error)
      return false
    }
  },
}