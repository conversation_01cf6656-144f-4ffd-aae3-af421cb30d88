# 前后端集成配置指南

## 配置概述

本项目已配置好前后端集成所需的代理设置和环境配置，支持开发环境下的热重载和生产环境的部署。

## 环境配置

### 开发环境 (.env.development)
- 前端开发服务器端口: 3000
- API代理路径: `/api` → `http://localhost/polarion/checklist`
- 支持热重载和开发者工具

### 生产环境 (.env.production)
- API基础路径: `/polarion/checklist`
- 优化构建配置

## 启动步骤

### 1. 确保后端服务运行
确保您的 Tomcat 服务器正在运行，后端服务可通过以下地址访问：
```
http://localhost/polarion/checklist
```

### 2. 安装依赖
```bash
cd frontend
npm install
```

### 3. 启动开发服务器
```bash
# 开发模式启动
npm run dev

# 或者使用
npm start
```

### 4. 访问应用
打开浏览器访问: http://localhost:3000

## 验证连接

### 方法1: 使用开发者工具
1. 启动前端应用后，右上角会显示"🛠️ 开发者工具"面板
2. 点击"测试后端连接"按钮验证基础连接
3. 点击"运行完整测试"按钮测试所有API端点

### 方法2: 查看控制台日志
1. 打开浏览器开发者工具 (F12)
2. 查看 Console 标签页
3. 观察代理请求和响应日志

### 方法3: 网络面板检查
1. 打开浏览器开发者工具 (F12)
2. 切换到 Network 标签页
3. 发起API请求，观察请求是否正确转发

## 代理配置说明

Vite 开发服务器配置了以下代理规则：
- 前端请求 `/api/*` → 后端 `http://localhost/polarion/checklist/*`
- 自动处理跨域问题
- 支持请求/响应日志记录

## 构建部署

### 开发构建
```bash
npm run build:dev
```

### 生产构建
```bash
npm run build:prod
```

构建产物位于 `dist/` 目录，可直接部署到 Web 服务器。

## 故障排除

### 连接失败
1. 检查后端服务是否正常运行
2. 确认后端地址配置正确
3. 查看浏览器控制台错误信息
4. 检查网络防火墙设置

### 跨域问题
- 开发环境：由 Vite 代理自动处理
- 生产环境：需要后端配置 CORS 头

### 端口冲突
如需修改前端端口，编辑 `.env.development` 文件中的 `VITE_DEV_PORT` 配置。

## 环境变量说明

| 变量名 | 开发环境 | 生产环境 | 说明 |
|--------|----------|----------|------|
| VITE_API_BASE_URL | /api | /polarion/checklist | API基础路径 |
| VITE_BACKEND_HOST | localhost | - | 后端主机地址 |
| VITE_BACKEND_PATH | /polarion/checklist | - | 后端路径 |
| VITE_DEV_PORT | 3000 | - | 开发服务器端口 |
| VITE_DEBUG | true | false | 调试模式开关 |

## 开发建议

1. 使用开发者工具面板快速测试连接状态
2. 关注浏览器控制台的代理日志
3. 定期运行完整连接测试确保集成正常
4. 遇到问题时先检查环境配置和后端服务状态
