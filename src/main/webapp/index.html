<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checklist Review System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info {
            margin: 20px 0;
            padding: 15px;
            background: #e8f4fd;
            border-left: 4px solid #2196F3;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .links a:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Checklist Review System</h1>
        <div class="info">
            <h3>系统说明</h3>
            <p>这是一个支持单人和多人评审的 checklist 系统。系统分为后台管理端和前台评审端两个部分。</p>
            <ul>
                <li><strong>后台管理端</strong>：用于管理 checklist 模板和配置</li>
                <li><strong>前台评审端</strong>：根据不同类型加载相应的检查单进行评审</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>API 接口</h3>
            <p>系统提供 REST API 接口，基础路径为 <code>/api</code></p>
            <ul>
                <li>模板管理：<code>/api/templates</code></li>
                <li>评审管理：<code>/api/reviews</code></li>
            </ul>
        </div>

        <div class="links">
            <a href="/api/templates" target="_blank">模板 API</a>
            <a href="/api/reviews" target="_blank">评审 API</a>
        </div>
    </div>
</body>
</html>