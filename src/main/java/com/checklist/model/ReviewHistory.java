package com.checklist.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * 评审历史数据模型
 */
public class ReviewHistory {
    
    @JsonProperty("reviewId")
    private String reviewId;
    
    @JsonProperty("sequence")
    private Integer sequence;
    
    @JsonProperty("reviewer")
    private String reviewer;
    
    @JsonProperty("reviewTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;
    
    @JsonProperty("action")
    private String action;
    
    @JsonProperty("details")
    private Map<String, Object> details;
    
    // 默认构造函数
    public ReviewHistory() {}
    
    // 全参构造函数
    public ReviewHistory(String reviewId, Integer sequence, String reviewer, 
                        LocalDateTime reviewTime, String action, 
                        Map<String, Object> details) {
        this.reviewId = reviewId;
        this.sequence = sequence;
        this.reviewer = reviewer;
        this.reviewTime = reviewTime;
        this.action = action;
        this.details = details;
    }
    
    // Getters and Setters
    public String getReviewId() {
        return reviewId;
    }
    
    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }
    
    public Integer getSequence() {
        return sequence;
    }
    
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }
    
    public String getReviewer() {
        return reviewer;
    }
    
    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }
    
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }
    
    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public Map<String, Object> getDetails() {
        return details;
    }
    
    public void setDetails(Map<String, Object> details) {
        this.details = details;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReviewHistory that = (ReviewHistory) o;
        return Objects.equals(reviewId, that.reviewId) &&
               Objects.equals(sequence, that.sequence) &&
               Objects.equals(reviewer, that.reviewer) &&
               Objects.equals(reviewTime, that.reviewTime);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(reviewId, sequence, reviewer, reviewTime);
    }
    
    @Override
    public String toString() {
        return "ReviewHistory{" +
               "reviewId='" + reviewId + '\'' +
               ", sequence=" + sequence +
               ", reviewer='" + reviewer + '\'' +
               ", reviewTime=" + reviewTime +
               ", action='" + action + '\'' +
               ", details=" + details +
               '}';
    }
}