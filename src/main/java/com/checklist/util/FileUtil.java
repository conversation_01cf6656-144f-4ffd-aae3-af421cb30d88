package com.checklist.util;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 文件操作工具类，包含文件锁机制
 */
public class FileUtil {
    
    private static final int LOCK_TIMEOUT_SECONDS = 30;
    private static final String BACKUP_SUFFIX = ".backup";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static String readFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            return null;
        }
        return Files.readString(path, StandardCharsets.UTF_8);
    }
    
    /**
     * 使用文件锁读取文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static String readFileWithLock(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            return null;
        }
        
        try (RandomAccessFile file = new RandomAccessFile(path.toFile(), "r");
             FileChannel channel = file.getChannel()) {
            
            FileLock lock = null;
            try {
                // 尝试获取共享锁（读锁）
                lock = channel.tryLock(0L, Long.MAX_VALUE, true);
                if (lock == null) {
                    throw new IOException("无法获取文件读锁: " + filePath);
                }
                
                byte[] bytes = new byte[(int) file.length()];
                file.readFully(bytes);
                return new String(bytes, StandardCharsets.UTF_8);
                
            } finally {
                if (lock != null) {
                    lock.release();
                }
            }
        }
    }
    
    /**
     * 写入文件内容
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @throws IOException IO异常
     */
    public static void writeFile(String filePath, String content) throws IOException {
        Path path = Paths.get(filePath);
        // 确保父目录存在
        Files.createDirectories(path.getParent());
        Files.writeString(path, content, StandardCharsets.UTF_8);
    }
    
    /**
     * 使用文件锁写入文件内容
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @throws IOException IO异常
     */
    public static void writeFileWithLock(String filePath, String content) throws IOException {
        Path path = Paths.get(filePath);
        // 确保父目录存在
        Files.createDirectories(path.getParent());
        
        try (RandomAccessFile file = new RandomAccessFile(path.toFile(), "rw");
             FileChannel channel = file.getChannel()) {
            
            FileLock lock = null;
            try {
                // 尝试获取独占锁（写锁）
                lock = channel.tryLock();
                if (lock == null) {
                    throw new IOException("无法获取文件写锁: " + filePath);
                }
                
                // 清空文件内容
                file.setLength(0);
                // 写入新内容
                byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
                file.write(bytes);
                
            } finally {
                if (lock != null) {
                    lock.release();
                }
            }
        }
    }
    
    /**
     * 安全写入文件（先备份，再写入）
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @throws IOException IO异常
     */
    public static void safeWriteFile(String filePath, String content) throws IOException {
        Path path = Paths.get(filePath);
        
        // 如果文件存在，先创建备份
        if (Files.exists(path)) {
            createBackup(filePath);
        }
        
        try {
            writeFileWithLock(filePath, content);
        } catch (IOException e) {
            // 写入失败，尝试恢复备份
            restoreBackup(filePath);
            throw e;
        }
    }
    
    /**
     * 创建文件备份
     * 
     * @param filePath 原文件路径
     * @throws IOException IO异常
     */
    public static void createBackup(String filePath) throws IOException {
        Path sourcePath = Paths.get(filePath);
        if (!Files.exists(sourcePath)) {
            return;
        }
        
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String backupPath = filePath + BACKUP_SUFFIX + "_" + timestamp;
        Files.copy(sourcePath, Paths.get(backupPath), StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * 恢复文件备份
     * 
     * @param filePath 原文件路径
     * @throws IOException IO异常
     */
    public static void restoreBackup(String filePath) throws IOException {
        String backupPattern = filePath + BACKUP_SUFFIX + "_";
        Path parentDir = Paths.get(filePath).getParent();
        
        if (parentDir == null || !Files.exists(parentDir)) {
            return;
        }
        
        // 查找最新的备份文件
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(parentDir, 
                path -> path.getFileName().toString().startsWith(Paths.get(filePath).getFileName() + BACKUP_SUFFIX))) {
            
            Path latestBackup = null;
            for (Path backup : stream) {
                if (latestBackup == null || 
                    Files.getLastModifiedTime(backup).compareTo(Files.getLastModifiedTime(latestBackup)) > 0) {
                    latestBackup = backup;
                }
            }
            
            if (latestBackup != null) {
                Files.copy(latestBackup, Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);
            }
        }
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void deleteFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (Files.exists(path)) {
            Files.delete(path);
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }
    
    /**
     * 创建目录
     * 
     * @param dirPath 目录路径
     * @throws IOException IO异常
     */
    public static void createDirectories(String dirPath) throws IOException {
        Path path = Paths.get(dirPath);
        Files.createDirectories(path);
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     * @throws IOException IO异常
     */
    public static long getFileSize(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            return 0;
        }
        return Files.size(path);
    }
    
    /**
     * 获取文件最后修改时间
     * 
     * @param filePath 文件路径
     * @return 最后修改时间
     * @throws IOException IO异常
     */
    public static LocalDateTime getLastModifiedTime(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            return null;
        }
        return LocalDateTime.ofInstant(
            Files.getLastModifiedTime(path).toInstant(),
            java.time.ZoneId.systemDefault()
        );
    }
    
    /**
     * 清理过期的备份文件
     * 
     * @param filePath 原文件路径
     * @param retentionDays 保留天数
     * @throws IOException IO异常
     */
    public static void cleanupBackups(String filePath, int retentionDays) throws IOException {
        String backupPattern = Paths.get(filePath).getFileName() + BACKUP_SUFFIX + "_";
        Path parentDir = Paths.get(filePath).getParent();
        
        if (parentDir == null || !Files.exists(parentDir)) {
            return;
        }
        
        long cutoffTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(retentionDays);
        
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(parentDir, 
                path -> path.getFileName().toString().startsWith(backupPattern))) {
            
            for (Path backup : stream) {
                if (Files.getLastModifiedTime(backup).toMillis() < cutoffTime) {
                    Files.delete(backup);
                }
            }
        }
    }
}