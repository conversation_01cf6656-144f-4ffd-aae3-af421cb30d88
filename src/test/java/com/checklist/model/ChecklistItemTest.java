package com.checklist.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistItem 模型测试类
 */
class ChecklistItemTest {
    
    private ChecklistItem item;
    
    @BeforeEach
    void setUp() {
        item = new ChecklistItem("item1", 1, "检查项内容", true, "category1");
    }
    
    @Test
    void testConstructorAndGetters() {
        assertEquals("item1", item.getId());
        assertEquals(Integer.valueOf(1), item.getSequence());
        assertEquals("检查项内容", item.getContent());
        assertEquals(Boolean.TRUE, item.getRequired());
        assertEquals("category1", item.getCategory());
    }
    
    @Test
    void testSetters() {
        item.setId("item2");
        item.setSequence(2);
        item.setContent("新检查项内容");
        item.setRequired(false);
        item.setCategory("category2");
        
        assertEquals("item2", item.getId());
        assertEquals(Integer.valueOf(2), item.getSequence());
        assertEquals("新检查项内容", item.getContent());
        assertEquals(Boolean.FALSE, item.getRequired());
        assertEquals("category2", item.getCategory());
    }
    
    @Test
    void testDefaultConstructor() {
        ChecklistItem emptyItem = new ChecklistItem();
        assertNull(emptyItem.getId());
        assertNull(emptyItem.getSequence());
        assertNull(emptyItem.getContent());
        assertNull(emptyItem.getRequired());
        assertNull(emptyItem.getCategory());
    }
    
    @Test
    void testEquals() {
        ChecklistItem item2 = new ChecklistItem("item1", 1, "检查项内容", true, "category1");
        ChecklistItem item3 = new ChecklistItem("item2", 1, "检查项内容", true, "category1");
        
        assertEquals(item, item2);
        assertNotEquals(item, item3);
        assertNotEquals(item, null);
        assertNotEquals(item, "string");
    }
    
    @Test
    void testHashCode() {
        ChecklistItem item2 = new ChecklistItem("item1", 1, "检查项内容", true, "category1");
        assertEquals(item.hashCode(), item2.hashCode());
    }
    
    @Test
    void testToString() {
        String toString = item.toString();
        assertTrue(toString.contains("ChecklistItem"));
        assertTrue(toString.contains("item1"));
        assertTrue(toString.contains("检查项内容"));
        assertTrue(toString.contains("true"));
        assertTrue(toString.contains("category1"));
    }
}