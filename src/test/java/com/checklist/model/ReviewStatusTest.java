package com.checklist.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReviewStatus 枚举测试类
 */
class ReviewStatusTest {
    
    @Test
    void testEnumValues() {
        assertEquals("PASS", ReviewStatus.PASS.getCode());
        assertEquals("通过", ReviewStatus.PASS.getDescription());
        
        assertEquals("FAIL", ReviewStatus.FAIL.getCode());
        assertEquals("不通过", ReviewStatus.FAIL.getDescription());
        
        assertEquals("SKIP", ReviewStatus.SKIP.getCode());
        assertEquals("暂不处理", ReviewStatus.SKIP.getDescription());
        
        assertEquals("PENDING", ReviewStatus.PENDING.getCode());
        assertEquals("待处理", ReviewStatus.PENDING.getDescription());
    }
    
    @Test
    void testFromCode() {
        assertEquals(ReviewStatus.PASS, ReviewStatus.fromCode("PASS"));
        assertEquals(ReviewStatus.FAIL, ReviewStatus.fromCode("FAIL"));
        assertEquals(ReviewStatus.SKIP, ReviewStatus.fromCode("SKIP"));
        assertEquals(ReviewStatus.PENDING, ReviewStatus.fromCode("PENDING"));
    }
    
    @Test
    void testFromCodeInvalid() {
        assertThrows(IllegalArgumentException.class, () -> {
            ReviewStatus.fromCode("INVALID");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            ReviewStatus.fromCode(null);
        });
    }
    
    @Test
    void testToString() {
        assertEquals("PASS", ReviewStatus.PASS.toString());
        assertEquals("FAIL", ReviewStatus.FAIL.toString());
        assertEquals("SKIP", ReviewStatus.SKIP.toString());
        assertEquals("PENDING", ReviewStatus.PENDING.toString());
    }
    
    @Test
    void testAllValues() {
        ReviewStatus[] values = ReviewStatus.values();
        assertEquals(4, values.length);
        
        boolean foundPass = false, foundFail = false, foundSkip = false, foundPending = false;
        for (ReviewStatus status : values) {
            switch (status) {
                case PASS:
                    foundPass = true;
                    break;
                case FAIL:
                    foundFail = true;
                    break;
                case SKIP:
                    foundSkip = true;
                    break;
                case PENDING:
                    foundPending = true;
                    break;
            }
        }
        
        assertTrue(foundPass && foundFail && foundSkip && foundPending);
    }
}