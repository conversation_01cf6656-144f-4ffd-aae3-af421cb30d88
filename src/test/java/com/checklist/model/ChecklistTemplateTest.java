package com.checklist.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistTemplate 模型测试类
 */
class ChecklistTemplateTest {
    
    private ChecklistTemplate template;
    private List<ChecklistItem> items;
    private LocalDateTime now;
    
    @BeforeEach
    void setUp() {
        now = LocalDateTime.now();
        
        ChecklistItem item1 = new ChecklistItem("item1", 1, "检查项1", true, "category1");
        ChecklistItem item2 = new ChecklistItem("item2", 2, "检查项2", false, "category2");
        items = Arrays.asList(item1, item2);
        
        template = new ChecklistTemplate("template1", "测试模板", "type1", "1.0", now, now, items);
    }
    
    @Test
    void testConstructorAndGetters() {
        assertEquals("template1", template.getId());
        assertEquals("测试模板", template.getName());
        assertEquals("type1", template.getType());
        assertEquals("1.0", template.getVersion());
        assertEquals(now, template.getCreatedTime());
        assertEquals(now, template.getUpdatedTime());
        assertEquals(items, template.getItems());
    }
    
    @Test
    void testSetters() {
        LocalDateTime newTime = LocalDateTime.now().plusHours(1);
        ChecklistItem newItem = new ChecklistItem("item3", 3, "检查项3", true, "category3");
        List<ChecklistItem> newItems = Arrays.asList(newItem);
        
        template.setId("template2");
        template.setName("新模板");
        template.setType("type2");
        template.setVersion("2.0");
        template.setCreatedTime(newTime);
        template.setUpdatedTime(newTime);
        template.setItems(newItems);
        
        assertEquals("template2", template.getId());
        assertEquals("新模板", template.getName());
        assertEquals("type2", template.getType());
        assertEquals("2.0", template.getVersion());
        assertEquals(newTime, template.getCreatedTime());
        assertEquals(newTime, template.getUpdatedTime());
        assertEquals(newItems, template.getItems());
    }
    
    @Test
    void testDefaultConstructor() {
        ChecklistTemplate emptyTemplate = new ChecklistTemplate();
        assertNull(emptyTemplate.getId());
        assertNull(emptyTemplate.getName());
        assertNull(emptyTemplate.getType());
        assertNull(emptyTemplate.getVersion());
        assertNull(emptyTemplate.getCreatedTime());
        assertNull(emptyTemplate.getUpdatedTime());
        assertNull(emptyTemplate.getItems());
    }
    
    @Test
    void testEquals() {
        ChecklistTemplate template2 = new ChecklistTemplate("template1", "测试模板", "type1", "1.0", now, now, items);
        ChecklistTemplate template3 = new ChecklistTemplate("template2", "测试模板", "type1", "1.0", now, now, items);
        
        assertEquals(template, template2);
        assertNotEquals(template, template3);
        assertNotEquals(template, null);
        assertNotEquals(template, "string");
    }
    
    @Test
    void testHashCode() {
        ChecklistTemplate template2 = new ChecklistTemplate("template1", "测试模板", "type1", "1.0", now, now, items);
        assertEquals(template.hashCode(), template2.hashCode());
    }
    
    @Test
    void testToString() {
        String toString = template.toString();
        assertTrue(toString.contains("ChecklistTemplate"));
        assertTrue(toString.contains("template1"));
        assertTrue(toString.contains("测试模板"));
        assertTrue(toString.contains("type1"));
        assertTrue(toString.contains("1.0"));
    }
}