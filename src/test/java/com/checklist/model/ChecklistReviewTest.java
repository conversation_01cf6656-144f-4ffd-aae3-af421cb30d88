package com.checklist.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistReview 模型测试类
 */
class ChecklistReviewTest {
    
    private ChecklistReview review;
    private List<ReviewItem> reviewItems;
    private LocalDateTime now;
    
    @BeforeEach
    void setUp() {
        now = LocalDateTime.now();
        
        ReviewItem item1 = new ReviewItem("item1", ReviewStatus.PASS, "通过", null);
        ReviewItem item2 = new ReviewItem("item2", ReviewStatus.FAIL, "不通过原因", null);
        reviewItems = Arrays.asList(item1, item2);
        
        review = new ChecklistReview("review1", "template1", "1.0", "type1", now, "IN_PROGRESS", reviewItems);
    }
    
    @Test
    void testConstructorAndGetters() {
        assertEquals("review1", review.getId());
        assertEquals("template1", review.getTemplateId());
        assertEquals("1.0", review.getTemplateVersion());
        assertEquals("type1", review.getType());
        assertEquals(now, review.getCreatedTime());
        assertEquals("IN_PROGRESS", review.getStatus());
        assertEquals(reviewItems, review.getReviewItems());
    }
    
    @Test
    void testSetters() {
        LocalDateTime newTime = LocalDateTime.now().plusHours(1);
        ReviewItem newItem = new ReviewItem("item3", ReviewStatus.SKIP, "暂不处理", null);
        List<ReviewItem> newItems = Arrays.asList(newItem);
        
        review.setId("review2");
        review.setTemplateId("template2");
        review.setTemplateVersion("2.0");
        review.setType("type2");
        review.setCreatedTime(newTime);
        review.setStatus("COMPLETED");
        review.setReviewItems(newItems);
        
        assertEquals("review2", review.getId());
        assertEquals("template2", review.getTemplateId());
        assertEquals("2.0", review.getTemplateVersion());
        assertEquals("type2", review.getType());
        assertEquals(newTime, review.getCreatedTime());
        assertEquals("COMPLETED", review.getStatus());
        assertEquals(newItems, review.getReviewItems());
    }
    
    @Test
    void testDefaultConstructor() {
        ChecklistReview emptyReview = new ChecklistReview();
        assertNull(emptyReview.getId());
        assertNull(emptyReview.getTemplateId());
        assertNull(emptyReview.getTemplateVersion());
        assertNull(emptyReview.getType());
        assertNull(emptyReview.getCreatedTime());
        assertNull(emptyReview.getStatus());
        assertNull(emptyReview.getReviewItems());
    }
    
    @Test
    void testEquals() {
        ChecklistReview review2 = new ChecklistReview("review1", "template1", "1.0", "type1", now, "IN_PROGRESS", reviewItems);
        ChecklistReview review3 = new ChecklistReview("review2", "template1", "1.0", "type1", now, "IN_PROGRESS", reviewItems);
        
        assertEquals(review, review2);
        assertNotEquals(review, review3);
        assertNotEquals(review, null);
        assertNotEquals(review, "string");
    }
    
    @Test
    void testHashCode() {
        ChecklistReview review2 = new ChecklistReview("review1", "template1", "1.0", "type1", now, "IN_PROGRESS", reviewItems);
        assertEquals(review.hashCode(), review2.hashCode());
    }
    
    @Test
    void testToString() {
        String toString = review.toString();
        assertTrue(toString.contains("ChecklistReview"));
        assertTrue(toString.contains("review1"));
        assertTrue(toString.contains("template1"));
        assertTrue(toString.contains("1.0"));
        assertTrue(toString.contains("type1"));
        assertTrue(toString.contains("IN_PROGRESS"));
    }
}