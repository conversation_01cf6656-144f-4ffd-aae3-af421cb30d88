package com.checklist.exception;

import com.checklist.model.ErrorResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * GlobalExceptionHandler 单元测试
 */
public class GlobalExceptionHandlerTest {
    
    private GlobalExceptionHandler exceptionHandler;
    
    @Mock
    private WebRequest webRequest;
    
    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        exceptionHandler = new GlobalExceptionHandler();
        when(webRequest.getDescription(false)).thenReturn("uri=/api/test");
    }
    
    @Test
    public void testHandleTemplateNotFoundException() {
        String templateId = "template123";
        TemplateNotFoundException exception = new TemplateNotFoundException(templateId);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleTemplateNotFoundException(exception, webRequest);
        
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("TEMPLATE_NOT_FOUND", errorResponse.getErrorCode());
        assertEquals("模板不存在: " + templateId, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
        assertNotNull(errorResponse.getTimestamp());
    }
    
    @Test
    public void testHandleReviewNotFoundException() {
        String reviewId = "review123";
        ReviewNotFoundException exception = new ReviewNotFoundException(reviewId);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleReviewNotFoundException(exception, webRequest);
        
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("REVIEW_NOT_FOUND", errorResponse.getErrorCode());
        assertEquals("评审实例不存在: " + reviewId, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleValidationException() {
        String message = "验证失败";
        ValidationException exception = new ValidationException(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleValidationException(exception, webRequest);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("VALIDATION_ERROR", errorResponse.getErrorCode());
        assertEquals(message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleFileAccessException() {
        String message = "文件访问失败";
        FileAccessException exception = new FileAccessException(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleFileAccessException(exception, webRequest);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("FILE_ACCESS_ERROR", errorResponse.getErrorCode());
        assertEquals(message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleExcelImportException() {
        String message = "Excel 导入失败";
        ExcelImportException exception = new ExcelImportException(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleExcelImportException(exception, webRequest);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("EXCEL_IMPORT_ERROR", errorResponse.getErrorCode());
        assertEquals(message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleChecklistException() {
        String errorCode = "CUSTOM_ERROR";
        String message = "自定义异常";
        ChecklistException exception = new ChecklistException(errorCode, message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleChecklistException(exception, webRequest);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals(errorCode, errorResponse.getErrorCode());
        assertEquals(message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleIllegalArgumentException() {
        String message = "参数错误";
        IllegalArgumentException exception = new IllegalArgumentException(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleIllegalArgumentException(exception, webRequest);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("INVALID_PARAMETER", errorResponse.getErrorCode());
        assertEquals(message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleIOException() {
        String message = "IO 错误";
        IOException exception = new IOException(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleIOException(exception, webRequest);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("IO_ERROR", errorResponse.getErrorCode());
        assertEquals("文件操作失败: " + message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleMaxUploadSizeExceededException() {
        MaxUploadSizeExceededException exception = new MaxUploadSizeExceededException(1024);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleMaxUploadSizeExceededException(exception, webRequest);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("FILE_SIZE_EXCEEDED", errorResponse.getErrorCode());
        assertEquals("上传文件大小超过限制", errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleRuntimeException() {
        String message = "运行时错误";
        RuntimeException exception = new RuntimeException(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleRuntimeException(exception, webRequest);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("RUNTIME_ERROR", errorResponse.getErrorCode());
        assertEquals("系统运行时错误: " + message, errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
    
    @Test
    public void testHandleGenericException() {
        String message = "未知错误";
        Exception exception = new Exception(message);
        
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleGenericException(exception, webRequest);
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ErrorResponse errorResponse = response.getBody();
        assertFalse(errorResponse.isSuccess());
        assertEquals("UNKNOWN_ERROR", errorResponse.getErrorCode());
        assertEquals("系统发生未知错误，请联系管理员", errorResponse.getMessage());
        assertEquals("/api/test", errorResponse.getPath());
    }
}