package com.checklist.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TemplateNotFoundException 单元测试
 */
public class TemplateNotFoundExceptionTest {
    
    @Test
    public void testTemplateNotFoundExceptionWithId() {
        String templateId = "template123";
        
        TemplateNotFoundException exception = new TemplateNotFoundException(templateId);
        
        assertEquals("TEMPLATE_NOT_FOUND", exception.getErrorCode());
        assertEquals("模板不存在: " + templateId, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    public void testTemplateNotFoundExceptionWithCause() {
        String templateId = "template123";
        Throwable cause = new RuntimeException("原因异常");
        
        TemplateNotFoundException exception = new TemplateNotFoundException(templateId, cause);
        
        assertEquals("TEMPLATE_NOT_FOUND", exception.getErrorCode());
        assertEquals("模板不存在: " + templateId, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    public void testTemplateNotFoundExceptionInheritance() {
        TemplateNotFoundException exception = new TemplateNotFoundException("test");
        
        assertTrue(exception instanceof ChecklistException);
        assertTrue(exception instanceof RuntimeException);
    }
}