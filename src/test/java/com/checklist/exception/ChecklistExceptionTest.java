package com.checklist.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistException 单元测试
 */
public class ChecklistExceptionTest {
    
    @Test
    public void testChecklistExceptionWithMessage() {
        String errorCode = "TEST_ERROR";
        String message = "测试异常消息";
        
        ChecklistException exception = new ChecklistException(errorCode, message);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    public void testChecklistExceptionWithCause() {
        String errorCode = "TEST_ERROR";
        String message = "测试异常消息";
        Throwable cause = new RuntimeException("原因异常");
        
        ChecklistException exception = new ChecklistException(errorCode, message, cause);
        
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    public void testChecklistExceptionInheritance() {
        ChecklistException exception = new ChecklistException("TEST", "测试");
        
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }
}