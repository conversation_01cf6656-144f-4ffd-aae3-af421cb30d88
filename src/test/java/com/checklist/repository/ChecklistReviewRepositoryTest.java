package com.checklist.repository;

import com.checklist.model.ChecklistReview;
import com.checklist.model.ReviewItem;
import com.checklist.model.ReviewRecord;
import com.checklist.model.ReviewStatus;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistReviewRepository 测试类
 */
class ChecklistReviewRepositoryTest {
    
    @TempDir
    Path tempDir;
    
    private ChecklistReviewRepository repository;
    private ChecklistReview testReview1;
    private ChecklistReview testReview2;
    
    @BeforeEach
    void setUp() {
        // 使用临时目录创建仓库
        System.setProperty("user.dir", tempDir.toString());
        repository = new ChecklistReviewRepository();
        
        // 创建测试数据
        testReview1 = createTestReview("review-1", "template-1", "1.0", "代码评审", "PENDING");
        testReview2 = createTestReview("review-2", "template-2", "1.0", "设计评审", "COMPLETED");
    }
    
    @AfterEach
    void tearDown() throws IOException {
        // 清理测试数据
        if (repository != null) {
            repository.deleteAll();
        }
    }
    
    private ChecklistReview createTestReview(String id, String templateId, String templateVersion, 
                                           String type, String status) {
        ChecklistReview review = new ChecklistReview();
        review.setId(id);
        review.setTemplateId(templateId);
        review.setTemplateVersion(templateVersion);
        review.setType(type);
        review.setStatus(status);
        review.setCreatedTime(LocalDateTime.now());
        
        List<ReviewItem> reviewItems = new ArrayList<>();
        ReviewItem item = new ReviewItem();
        item.setItemId("item-1");
        item.setStatus(ReviewStatus.PENDING);
        item.setComment("");
        item.setReviewHistory(new ArrayList<>());
        reviewItems.add(item);
        
        review.setReviewItems(reviewItems);
        return review;
    }
    
    @Test
    void testSaveAndGenerateId() throws IOException {
        // 测试保存时自动生成ID
        ChecklistReview review = createTestReview(null, "template-1", "1.0", "测试评审", "PENDING");
        
        ChecklistReview saved = repository.save(review);
        
        assertNotNull(saved.getId());
        assertTrue(saved.getId().startsWith("review_"));
        assertEquals("测试评审", saved.getType());
    }
    
    @Test
    void testFindByTemplateId() throws IOException {
        // 保存测试数据
        repository.save(testReview1);
        repository.save(testReview2);
        
        // 创建同模板的另一个评审
        ChecklistReview review3 = createTestReview("review-3", "template-1", "2.0", "代码评审", "COMPLETED");
        repository.save(review3);
        
        // 测试根据模板ID查找
        List<ChecklistReview> reviews = repository.findByTemplateId("template-1");
        
        assertEquals(2, reviews.size());
        assertTrue(reviews.stream().anyMatch(r -> r.getId().equals("review-1")));
        assertTrue(reviews.stream().anyMatch(r -> r.getId().equals("review-3")));
    }
    
    @Test
    void testFindByTemplateIdEmpty() throws IOException {
        // 测试查找不存在的模板ID
        List<ChecklistReview> reviews = repository.findByTemplateId("non-existent-template");
        
        assertTrue(reviews.isEmpty());
    }
    
    @Test
    void testFindByTemplateIdWithNullOrEmpty() {
        // 测试使用null或空字符串查找
        assertThrows(IllegalArgumentException.class, () -> repository.findByTemplateId(null));
        assertThrows(IllegalArgumentException.class, () -> repository.findByTemplateId(""));
        assertThrows(IllegalArgumentException.class, () -> repository.findByTemplateId("   "));
    }
    
    @Test
    void testFindByType() throws IOException {
        // 保存测试数据
        repository.save(testReview1);
        repository.save(testReview2);
        
        // 创建同类型的另一个评审
        ChecklistReview review3 = createTestReview("review-3", "template-3", "1.0", "代码评审", "IN_PROGRESS");
        repository.save(review3);
        
        // 测试根据类型查找
        List<ChecklistReview> codeReviews = repository.findByType("代码评审");
        
        assertEquals(2, codeReviews.size());
        assertTrue(codeReviews.stream().anyMatch(r -> r.getId().equals("review-1")));
        assertTrue(codeReviews.stream().anyMatch(r -> r.getId().equals("review-3")));
    }
    
    @Test
    void testFindByTypeWithNullOrEmpty() {
        // 测试使用null或空字符串查找
        assertThrows(IllegalArgumentException.class, () -> repository.findByType(null));
        assertThrows(IllegalArgumentException.class, () -> repository.findByType(""));
        assertThrows(IllegalArgumentException.class, () -> repository.findByType("   "));
    }
    
    @Test
    void testFindByStatus() throws IOException {
        // 保存测试数据
        repository.save(testReview1);
        repository.save(testReview2);
        
        // 创建同状态的另一个评审
        ChecklistReview review3 = createTestReview("review-3", "template-3", "1.0", "安全评审", "PENDING");
        repository.save(review3);
        
        // 测试根据状态查找
        List<ChecklistReview> pendingReviews = repository.findByStatus("PENDING");
        
        assertEquals(2, pendingReviews.size());
        assertTrue(pendingReviews.stream().anyMatch(r -> r.getId().equals("review-1")));
        assertTrue(pendingReviews.stream().anyMatch(r -> r.getId().equals("review-3")));
    }
    
    @Test
    void testFindByStatusWithNullOrEmpty() {
        // 测试使用null或空字符串查找
        assertThrows(IllegalArgumentException.class, () -> repository.findByStatus(null));
        assertThrows(IllegalArgumentException.class, () -> repository.findByStatus(""));
        assertThrows(IllegalArgumentException.class, () -> repository.findByStatus("   "));
    }
    
    @Test
    void testFindByCreatedTimeBetween() throws IOException {
        // 创建不同时间的评审
        LocalDateTime baseTime = LocalDateTime.now();
        
        ChecklistReview review1 = createTestReview("review-1", "template-1", "1.0", "代码评审", "PENDING");
        review1.setCreatedTime(baseTime.minusHours(3));
        repository.save(review1);
        
        ChecklistReview review2 = createTestReview("review-2", "template-2", "1.0", "设计评审", "COMPLETED");
        review2.setCreatedTime(baseTime.minusHours(1));
        repository.save(review2);
        
        ChecklistReview review3 = createTestReview("review-3", "template-3", "1.0", "安全评审", "IN_PROGRESS");
        review3.setCreatedTime(baseTime.plusHours(1));
        repository.save(review3);
        
        // 测试时间范围查找
        LocalDateTime startTime = baseTime.minusHours(2);
        LocalDateTime endTime = baseTime;
        List<ChecklistReview> reviews = repository.findByCreatedTimeBetween(startTime, endTime);
        
        assertEquals(1, reviews.size());
        assertEquals("review-2", reviews.get(0).getId());
    }
    
    @Test
    void testFindByCreatedTimeBetweenWithNullTimes() {
        // 测试使用null时间
        LocalDateTime now = LocalDateTime.now();
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByCreatedTimeBetween(null, now));
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByCreatedTimeBetween(now, null));
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByCreatedTimeBetween(null, null));
    }
    
    @Test
    void testFindByCreatedTimeBetweenWithInvalidRange() {
        // 测试无效时间范围
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime earlier = now.minusHours(1);
        
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByCreatedTimeBetween(now, earlier));
    }
    
    @Test
    void testFindByTemplateIdAndVersion() throws IOException {
        // 保存测试数据
        repository.save(testReview1);
        repository.save(testReview2);
        
        // 创建同模板不同版本的评审
        ChecklistReview review3 = createTestReview("review-3", "template-1", "2.0", "代码评审", "COMPLETED");
        repository.save(review3);
        
        // 测试根据模板ID和版本查找
        List<ChecklistReview> reviews = repository.findByTemplateIdAndVersion("template-1", "1.0");
        
        assertEquals(1, reviews.size());
        assertEquals("review-1", reviews.get(0).getId());
        assertEquals("1.0", reviews.get(0).getTemplateVersion());
    }
    
    @Test
    void testFindByTemplateIdAndVersionWithNullOrEmpty() {
        // 测试使用null或空字符串查找
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByTemplateIdAndVersion(null, "1.0"));
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByTemplateIdAndVersion("template-1", null));
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByTemplateIdAndVersion("", "1.0"));
        assertThrows(IllegalArgumentException.class, () -> 
            repository.findByTemplateIdAndVersion("template-1", ""));
    }
    
    @Test
    void testFindRecentReviews() throws IOException {
        // 创建不同时间的评审
        LocalDateTime baseTime = LocalDateTime.now();
        
        ChecklistReview review1 = createTestReview("review-1", "template-1", "1.0", "代码评审", "PENDING");
        review1.setCreatedTime(baseTime.minusHours(3));
        repository.save(review1);
        
        ChecklistReview review2 = createTestReview("review-2", "template-2", "1.0", "设计评审", "COMPLETED");
        review2.setCreatedTime(baseTime.minusHours(1));
        repository.save(review2);
        
        ChecklistReview review3 = createTestReview("review-3", "template-3", "1.0", "安全评审", "IN_PROGRESS");
        review3.setCreatedTime(baseTime);
        repository.save(review3);
        
        // 测试获取最近的评审
        List<ChecklistReview> recentReviews = repository.findRecentReviews(2);
        
        assertEquals(2, recentReviews.size());
        assertEquals("review-3", recentReviews.get(0).getId()); // 最新的
        assertEquals("review-2", recentReviews.get(1).getId()); // 第二新的
    }
    
    @Test
    void testFindRecentReviewsWithInvalidLimit() {
        // 测试无效的限制数量
        assertThrows(IllegalArgumentException.class, () -> repository.findRecentReviews(0));
        assertThrows(IllegalArgumentException.class, () -> repository.findRecentReviews(-1));
    }
    
    @Test
    void testCountByStatus() throws IOException {
        // 保存测试数据
        repository.save(testReview1); // PENDING
        repository.save(testReview2); // COMPLETED
        
        ChecklistReview review3 = createTestReview("review-3", "template-3", "1.0", "安全评审", "PENDING");
        repository.save(review3);
        
        // 测试统计状态数量
        long pendingCount = repository.countByStatus("PENDING");
        long completedCount = repository.countByStatus("COMPLETED");
        long inProgressCount = repository.countByStatus("IN_PROGRESS");
        
        assertEquals(2, pendingCount);
        assertEquals(1, completedCount);
        assertEquals(0, inProgressCount);
    }
    
    @Test
    void testCountByStatusWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串统计
        assertEquals(0, repository.countByStatus(null));
        assertEquals(0, repository.countByStatus(""));
        assertEquals(0, repository.countByStatus("   "));
    }
    
    @Test
    void testCountByType() throws IOException {
        // 保存测试数据
        repository.save(testReview1); // 代码评审
        repository.save(testReview2); // 设计评审
        
        ChecklistReview review3 = createTestReview("review-3", "template-3", "1.0", "代码评审", "COMPLETED");
        repository.save(review3);
        
        // 测试统计类型数量
        long codeReviewCount = repository.countByType("代码评审");
        long designReviewCount = repository.countByType("设计评审");
        long securityReviewCount = repository.countByType("安全评审");
        
        assertEquals(2, codeReviewCount);
        assertEquals(1, designReviewCount);
        assertEquals(0, securityReviewCount);
    }
    
    @Test
    void testCountByTypeWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串统计
        assertEquals(0, repository.countByType(null));
        assertEquals(0, repository.countByType(""));
        assertEquals(0, repository.countByType("   "));
    }
    
    @Test
    void testExistsByTemplateId() throws IOException {
        // 保存测试数据
        repository.save(testReview1); // template-1
        repository.save(testReview2); // template-2
        
        // 测试存在的模板ID
        assertTrue(repository.existsByTemplateId("template-1"));
        assertTrue(repository.existsByTemplateId("template-2"));
        
        // 测试不存在的模板ID
        assertFalse(repository.existsByTemplateId("template-3"));
    }
    
    @Test
    void testExistsByTemplateIdWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串
        assertFalse(repository.existsByTemplateId(null));
        assertFalse(repository.existsByTemplateId(""));
        assertFalse(repository.existsByTemplateId("   "));
    }
    
    @Test
    void testConcurrentAccess() throws IOException, InterruptedException {
        // 测试并发访问
        final int threadCount = 3;
        final int operationsPerThread = 5;
        Thread[] threads = new Thread[threadCount];
        final Exception[] exceptions = new Exception[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        ChecklistReview review = createTestReview(
                            "review-" + threadIndex + "-" + j,
                            "template-" + threadIndex,
                            "1.0",
                            "类型-" + threadIndex,
                            "PENDING"
                        );
                        repository.save(review);
                        
                        // 读取操作
                        repository.findById(review.getId());
                        repository.findByType("类型-" + threadIndex);
                        repository.findByStatus("PENDING");
                    }
                } catch (Exception e) {
                    exceptions[threadIndex] = e;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 检查是否有异常
        for (int i = 0; i < threadCount; i++) {
            if (exceptions[i] != null) {
                fail("线程 " + i + " 发生异常: " + exceptions[i].getMessage());
            }
        }
        
        // 验证数据完整性
        long totalCount = repository.count();
        assertEquals(threadCount * operationsPerThread, totalCount);
    }
}