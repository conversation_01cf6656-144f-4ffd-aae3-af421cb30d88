package com.checklist.repository;

import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistItem;
import com.checklist.util.FileUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BaseJsonFileRepository 测试类
 */
class BaseJsonFileRepositoryTest {
    
    @TempDir
    Path tempDir;
    
    private TestRepository repository;
    private ChecklistTemplate testTemplate;
    
    @BeforeEach
    void setUp() {
        String dataDirectory = tempDir.toString() + "/test-templates";
        repository = new TestRepository(dataDirectory);
        
        // 创建测试数据
        testTemplate = new ChecklistTemplate();
        testTemplate.setId("test-template-1");
        testTemplate.setName("测试模板");
        testTemplate.setType("测试类型");
        testTemplate.setVersion("1.0");
        testTemplate.setCreatedTime(LocalDateTime.now());
        testTemplate.setUpdatedTime(LocalDateTime.now());
        
        List<ChecklistItem> items = new ArrayList<>();
        ChecklistItem item1 = new ChecklistItem();
        item1.setId("item-1");
        item1.setSequence(1);
        item1.setContent("检查项1");
        item1.setRequired(true);
        items.add(item1);
        
        testTemplate.setItems(items);
    }
    
    @AfterEach
    void tearDown() throws IOException {
        // 清理测试数据
        if (repository != null) {
            repository.deleteAll();
        }
    }
    
    @Test
    void testSave() throws IOException {
        // 测试保存新实体
        ChecklistTemplate saved = repository.save(testTemplate);
        
        assertNotNull(saved);
        assertEquals(testTemplate.getId(), saved.getId());
        assertEquals(testTemplate.getName(), saved.getName());
        
        // 验证文件是否创建
        assertTrue(FileUtil.fileExists(repository.getFilePath(testTemplate.getId())));
    }
    
    @Test
    void testSaveWithNullId() throws IOException {
        // 测试保存没有ID的实体
        testTemplate.setId(null);
        
        ChecklistTemplate saved = repository.save(testTemplate);
        
        assertNotNull(saved);
        assertNotNull(saved.getId());
        assertTrue(saved.getId().startsWith("test-"));
    }
    
    @Test
    void testSaveWithNullEntity() {
        // 测试保存空实体
        assertThrows(IllegalArgumentException.class, () -> repository.save(null));
    }
    
    @Test
    void testFindById() throws IOException {
        // 先保存实体
        repository.save(testTemplate);
        
        // 测试查找存在的实体
        Optional<ChecklistTemplate> found = repository.findById(testTemplate.getId());
        
        assertTrue(found.isPresent());
        assertEquals(testTemplate.getId(), found.get().getId());
        assertEquals(testTemplate.getName(), found.get().getName());
    }
    
    @Test
    void testFindByIdNotFound() throws IOException {
        // 测试查找不存在的实体
        Optional<ChecklistTemplate> found = repository.findById("non-existent-id");
        
        assertFalse(found.isPresent());
    }
    
    @Test
    void testFindByIdWithNull() throws IOException {
        // 测试使用null ID查找
        Optional<ChecklistTemplate> found = repository.findById(null);
        
        assertFalse(found.isPresent());
    }
    
    @Test
    void testFindAll() throws IOException {
        // 保存多个实体
        repository.save(testTemplate);
        
        ChecklistTemplate template2 = new ChecklistTemplate();
        template2.setId("test-template-2");
        template2.setName("测试模板2");
        template2.setType("测试类型");
        template2.setVersion("1.0");
        template2.setCreatedTime(LocalDateTime.now());
        template2.setUpdatedTime(LocalDateTime.now());
        template2.setItems(new ArrayList<>());
        repository.save(template2);
        
        // 测试查找所有实体
        List<ChecklistTemplate> all = repository.findAll();
        
        assertEquals(2, all.size());
        assertTrue(all.stream().anyMatch(t -> t.getId().equals(testTemplate.getId())));
        assertTrue(all.stream().anyMatch(t -> t.getId().equals(template2.getId())));
    }
    
    @Test
    void testFindAllEmpty() throws IOException {
        // 测试查找空列表
        List<ChecklistTemplate> all = repository.findAll();
        
        assertTrue(all.isEmpty());
    }
    
    @Test
    void testExistsById() throws IOException {
        // 测试不存在的实体
        assertFalse(repository.existsById(testTemplate.getId()));
        
        // 保存实体后测试
        repository.save(testTemplate);
        assertTrue(repository.existsById(testTemplate.getId()));
    }
    
    @Test
    void testExistsByIdWithNull() throws IOException {
        // 测试使用null ID
        assertFalse(repository.existsById(null));
    }
    
    @Test
    void testDeleteById() throws IOException {
        // 先保存实体
        repository.save(testTemplate);
        assertTrue(repository.existsById(testTemplate.getId()));
        
        // 删除实体
        repository.deleteById(testTemplate.getId());
        assertFalse(repository.existsById(testTemplate.getId()));
    }
    
    @Test
    void testDeleteByIdNotFound() throws IOException {
        // 测试删除不存在的实体（不应该抛出异常）
        assertDoesNotThrow(() -> repository.deleteById("non-existent-id"));
    }
    
    @Test
    void testDeleteByIdWithNull() throws IOException {
        // 测试使用null ID删除（不应该抛出异常）
        assertDoesNotThrow(() -> repository.deleteById(null));
    }
    
    @Test
    void testDelete() throws IOException {
        // 先保存实体
        repository.save(testTemplate);
        assertTrue(repository.existsById(testTemplate.getId()));
        
        // 删除实体
        repository.delete(testTemplate);
        assertFalse(repository.existsById(testTemplate.getId()));
    }
    
    @Test
    void testDeleteWithNull() throws IOException {
        // 测试删除null实体（不应该抛出异常）
        assertDoesNotThrow(() -> repository.delete(null));
    }
    
    @Test
    void testDeleteAll() throws IOException {
        // 保存多个实体
        repository.save(testTemplate);
        
        ChecklistTemplate template2 = new ChecklistTemplate();
        template2.setId("test-template-2");
        template2.setName("测试模板2");
        template2.setType("测试类型");
        template2.setVersion("1.0");
        template2.setCreatedTime(LocalDateTime.now());
        template2.setUpdatedTime(LocalDateTime.now());
        template2.setItems(new ArrayList<>());
        repository.save(template2);
        
        assertEquals(2, repository.count());
        
        // 删除所有实体
        repository.deleteAll();
        assertEquals(0, repository.count());
    }
    
    @Test
    void testCount() throws IOException {
        // 测试空仓库
        assertEquals(0, repository.count());
        
        // 保存实体后测试
        repository.save(testTemplate);
        assertEquals(1, repository.count());
        
        // 保存更多实体
        ChecklistTemplate template2 = new ChecklistTemplate();
        template2.setId("test-template-2");
        template2.setName("测试模板2");
        template2.setType("测试类型");
        template2.setVersion("1.0");
        template2.setCreatedTime(LocalDateTime.now());
        template2.setUpdatedTime(LocalDateTime.now());
        template2.setItems(new ArrayList<>());
        repository.save(template2);
        
        assertEquals(2, repository.count());
    }
    
    @Test
    void testFindBy() throws IOException {
        // 保存多个实体
        repository.save(testTemplate);
        
        ChecklistTemplate template2 = new ChecklistTemplate();
        template2.setId("test-template-2");
        template2.setName("测试模板2");
        template2.setType("其他类型");
        template2.setVersion("1.0");
        template2.setCreatedTime(LocalDateTime.now());
        template2.setUpdatedTime(LocalDateTime.now());
        template2.setItems(new ArrayList<>());
        repository.save(template2);
        
        // 根据类型查找
        List<ChecklistTemplate> found = repository.findBy(t -> "测试类型".equals(t.getType()));
        
        assertEquals(1, found.size());
        assertEquals(testTemplate.getId(), found.get(0).getId());
    }
    
    @Test
    void testFindFirstBy() throws IOException {
        // 保存多个实体
        repository.save(testTemplate);
        
        ChecklistTemplate template2 = new ChecklistTemplate();
        template2.setId("test-template-2");
        template2.setName("测试模板2");
        template2.setType("测试类型");
        template2.setVersion("1.0");
        template2.setCreatedTime(LocalDateTime.now());
        template2.setUpdatedTime(LocalDateTime.now());
        template2.setItems(new ArrayList<>());
        repository.save(template2);
        
        // 根据类型查找第一个
        Optional<ChecklistTemplate> found = repository.findFirstBy(t -> "测试类型".equals(t.getType()));
        
        assertTrue(found.isPresent());
        assertTrue(found.get().getId().equals(testTemplate.getId()) || 
                  found.get().getId().equals(template2.getId()));
    }
    
    /**
     * 测试用的Repository实现类
     */
    private static class TestRepository extends BaseJsonFileRepository<ChecklistTemplate, String> {
        
        public TestRepository(String dataDirectory) {
            super(dataDirectory, ChecklistTemplate.class, new TypeReference<List<ChecklistTemplate>>() {});
        }
        
        @Override
        protected String getEntityId(ChecklistTemplate entity) {
            return entity.getId();
        }
        
        @Override
        protected void setEntityId(ChecklistTemplate entity, String id) {
            entity.setId(id);
        }
        
        @Override
        protected String generateNewId() {
            return "test-" + UUID.randomUUID().toString().replace("-", "");
        }
        
        // 暴露受保护的方法用于测试
        public String getFilePath(String id) {
            return super.getFilePath(id);
        }
    }
}