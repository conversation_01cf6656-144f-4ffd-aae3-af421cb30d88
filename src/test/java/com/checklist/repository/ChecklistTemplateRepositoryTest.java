package com.checklist.repository;

import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistItem;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistTemplateRepository 测试类
 */
class ChecklistTemplateRepositoryTest {
    
    @TempDir
    Path tempDir;
    
    private ChecklistTemplateRepository repository;
    private ChecklistTemplate testTemplate1;
    private ChecklistTemplate testTemplate2;
    
    @BeforeEach
    void setUp() {
        // 使用临时目录创建仓库
        System.setProperty("user.dir", tempDir.toString());
        repository = new ChecklistTemplateRepository();
        
        // 创建测试数据
        testTemplate1 = createTestTemplate("template-1", "代码评审模板", "代码评审", "1.0");
        testTemplate2 = createTestTemplate("template-2", "设计评审模板", "设计评审", "1.0");
    }
    
    @AfterEach
    void tearDown() throws IOException {
        // 清理测试数据
        if (repository != null) {
            repository.deleteAll();
        }
    }
    
    private ChecklistTemplate createTestTemplate(String id, String name, String type, String version) {
        ChecklistTemplate template = new ChecklistTemplate();
        template.setId(id);
        template.setName(name);
        template.setType(type);
        template.setVersion(version);
        template.setCreatedTime(LocalDateTime.now());
        template.setUpdatedTime(LocalDateTime.now());
        
        List<ChecklistItem> items = new ArrayList<>();
        ChecklistItem item = new ChecklistItem();
        item.setId("item-1");
        item.setSequence(1);
        item.setContent("检查项1");
        item.setRequired(true);
        items.add(item);
        
        template.setItems(items);
        return template;
    }
    
    @Test
    void testSaveAndGenerateId() throws IOException {
        // 测试保存时自动生成ID
        ChecklistTemplate template = createTestTemplate(null, "新模板", "测试类型", "1.0");
        
        ChecklistTemplate saved = repository.save(template);
        
        assertNotNull(saved.getId());
        assertTrue(saved.getId().startsWith("template_"));
        assertEquals("新模板", saved.getName());
    }
    
    @Test
    void testFindByType() throws IOException {
        // 保存测试数据
        repository.save(testTemplate1);
        repository.save(testTemplate2);
        
        // 创建同类型的另一个模板
        ChecklistTemplate template3 = createTestTemplate("template-3", "代码评审模板2", "代码评审", "2.0");
        repository.save(template3);
        
        // 测试根据类型查找
        List<ChecklistTemplate> codeReviewTemplates = repository.findByType("代码评审");
        
        assertEquals(2, codeReviewTemplates.size());
        assertTrue(codeReviewTemplates.stream().anyMatch(t -> t.getId().equals("template-1")));
        assertTrue(codeReviewTemplates.stream().anyMatch(t -> t.getId().equals("template-3")));
    }
    
    @Test
    void testFindByTypeEmpty() throws IOException {
        // 测试查找不存在的类型
        List<ChecklistTemplate> templates = repository.findByType("不存在的类型");
        
        assertTrue(templates.isEmpty());
    }
    
    @Test
    void testFindByTypeWithNullOrEmpty() {
        // 测试使用null或空字符串查找
        assertThrows(IllegalArgumentException.class, () -> repository.findByType(null));
        assertThrows(IllegalArgumentException.class, () -> repository.findByType(""));
        assertThrows(IllegalArgumentException.class, () -> repository.findByType("   "));
    }
    
    @Test
    void testFindByName() throws IOException {
        // 保存测试数据
        repository.save(testTemplate1);
        repository.save(testTemplate2);
        
        // 测试根据名称查找
        Optional<ChecklistTemplate> found = repository.findByName("代码评审模板");
        
        assertTrue(found.isPresent());
        assertEquals("template-1", found.get().getId());
        assertEquals("代码评审模板", found.get().getName());
    }
    
    @Test
    void testFindByNameNotFound() throws IOException {
        // 测试查找不存在的名称
        Optional<ChecklistTemplate> found = repository.findByName("不存在的模板");
        
        assertFalse(found.isPresent());
    }
    
    @Test
    void testFindByNameWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串查找
        Optional<ChecklistTemplate> found1 = repository.findByName(null);
        Optional<ChecklistTemplate> found2 = repository.findByName("");
        Optional<ChecklistTemplate> found3 = repository.findByName("   ");
        
        assertFalse(found1.isPresent());
        assertFalse(found2.isPresent());
        assertFalse(found3.isPresent());
    }
    
    @Test
    void testFindByTypeAndVersion() throws IOException {
        // 保存测试数据
        repository.save(testTemplate1);
        repository.save(testTemplate2);
        
        // 创建同类型不同版本的模板
        ChecklistTemplate template3 = createTestTemplate("template-3", "代码评审模板v2", "代码评审", "2.0");
        repository.save(template3);
        
        // 测试根据类型和版本查找
        Optional<ChecklistTemplate> found = repository.findByTypeAndVersion("代码评审", "2.0");
        
        assertTrue(found.isPresent());
        assertEquals("template-3", found.get().getId());
        assertEquals("2.0", found.get().getVersion());
    }
    
    @Test
    void testFindByTypeAndVersionNotFound() throws IOException {
        // 保存测试数据
        repository.save(testTemplate1);
        
        // 测试查找不存在的类型和版本组合
        Optional<ChecklistTemplate> found = repository.findByTypeAndVersion("代码评审", "3.0");
        
        assertFalse(found.isPresent());
    }
    
    @Test
    void testFindByTypeAndVersionWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串查找
        Optional<ChecklistTemplate> found1 = repository.findByTypeAndVersion(null, "1.0");
        Optional<ChecklistTemplate> found2 = repository.findByTypeAndVersion("代码评审", null);
        Optional<ChecklistTemplate> found3 = repository.findByTypeAndVersion("", "1.0");
        Optional<ChecklistTemplate> found4 = repository.findByTypeAndVersion("代码评审", "");
        
        assertFalse(found1.isPresent());
        assertFalse(found2.isPresent());
        assertFalse(found3.isPresent());
        assertFalse(found4.isPresent());
    }
    
    @Test
    void testFindLatestByType() throws IOException {
        // 创建不同时间的模板
        LocalDateTime baseTime = LocalDateTime.now();
        
        ChecklistTemplate template1 = createTestTemplate("template-1", "代码评审模板v1", "代码评审", "1.0");
        template1.setUpdatedTime(baseTime.minusHours(2));
        repository.save(template1);
        
        ChecklistTemplate template2 = createTestTemplate("template-2", "代码评审模板v2", "代码评审", "2.0");
        template2.setUpdatedTime(baseTime.minusHours(1));
        repository.save(template2);
        
        ChecklistTemplate template3 = createTestTemplate("template-3", "代码评审模板v3", "代码评审", "3.0");
        template3.setUpdatedTime(baseTime);
        repository.save(template3);
        
        // 测试获取最新版本
        Optional<ChecklistTemplate> latest = repository.findLatestByType("代码评审");
        
        assertTrue(latest.isPresent());
        assertEquals("template-3", latest.get().getId());
        assertEquals("3.0", latest.get().getVersion());
    }
    
    @Test
    void testFindLatestByTypeNotFound() throws IOException {
        // 测试查找不存在的类型
        Optional<ChecklistTemplate> latest = repository.findLatestByType("不存在的类型");
        
        assertFalse(latest.isPresent());
    }
    
    @Test
    void testFindLatestByTypeWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串查找
        Optional<ChecklistTemplate> found1 = repository.findLatestByType(null);
        Optional<ChecklistTemplate> found2 = repository.findLatestByType("");
        Optional<ChecklistTemplate> found3 = repository.findLatestByType("   ");
        
        assertFalse(found1.isPresent());
        assertFalse(found2.isPresent());
        assertFalse(found3.isPresent());
    }
    
    @Test
    void testExistsByName() throws IOException {
        // 保存测试数据
        repository.save(testTemplate1);
        
        // 测试存在的名称
        assertTrue(repository.existsByName("代码评审模板"));
        
        // 测试不存在的名称
        assertFalse(repository.existsByName("不存在的模板"));
    }
    
    @Test
    void testExistsByNameWithExcludeId() throws IOException {
        // 保存测试数据
        repository.save(testTemplate1);
        
        // 测试排除自身ID
        assertFalse(repository.existsByName("代码评审模板", "template-1"));
        
        // 测试不排除其他ID
        assertTrue(repository.existsByName("代码评审模板", "other-id"));
    }
    
    @Test
    void testExistsByNameWithNullOrEmpty() throws IOException {
        // 测试使用null或空字符串
        assertFalse(repository.existsByName(null));
        assertFalse(repository.existsByName(""));
        assertFalse(repository.existsByName("   "));
    }
    
    @Test
    void testConcurrentAccess() throws IOException, InterruptedException {
        // 测试并发访问
        final int threadCount = 5;
        final int operationsPerThread = 10;
        Thread[] threads = new Thread[threadCount];
        final Exception[] exceptions = new Exception[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        ChecklistTemplate template = createTestTemplate(
                            "template-" + threadIndex + "-" + j,
                            "模板-" + threadIndex + "-" + j,
                            "类型-" + threadIndex,
                            "1.0"
                        );
                        repository.save(template);
                        
                        // 读取操作
                        repository.findById(template.getId());
                        repository.findByType("类型-" + threadIndex);
                    }
                } catch (Exception e) {
                    exceptions[threadIndex] = e;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 检查是否有异常
        for (int i = 0; i < threadCount; i++) {
            if (exceptions[i] != null) {
                fail("线程 " + i + " 发生异常: " + exceptions[i].getMessage());
            }
        }
        
        // 验证数据完整性
        long totalCount = repository.count();
        assertEquals(threadCount * operationsPerThread, totalCount);
    }
}