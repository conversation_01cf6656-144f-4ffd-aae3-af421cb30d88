package com.checklist.controller;

import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistItem;
import com.checklist.service.ChecklistTemplateService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ChecklistTemplateControllerTest {
    
    @Mock
    private ChecklistTemplateService templateService;
    
    @InjectMocks
    private ChecklistTemplateController templateController;
    
    private MockMvc mockMvc;
    private ChecklistTemplate testTemplate;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(templateController).build();
        
        // 创建测试模板
        testTemplate = new ChecklistTemplate();
        testTemplate.setId("template_001");
        testTemplate.setName("测试模板");
        testTemplate.setType("code_review");
        testTemplate.setVersion("1.0");
        
        ChecklistItem item1 = new ChecklistItem();
        item1.setId("item_001");
        item1.setSequence(1);
        item1.setContent("检查代码规范");
        item1.setRequired(true);
        
        ChecklistItem item2 = new ChecklistItem();
        item2.setId("item_002");
        item2.setSequence(2);
        item2.setContent("检查单元测试");
        item2.setRequired(false);
        
        testTemplate.setItems(Arrays.asList(item1, item2));
    }
    
    @Test
    void testGetAllTemplates_Success() throws Exception {
        // 准备测试数据
        List<ChecklistTemplate> templates = Arrays.asList(testTemplate);
        when(templateService.getAllTemplates()).thenReturn(templates);
        
        // 执行请求
        mockMvc.perform(get("/api/templates"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(templateService, times(1)).getAllTemplates();
    }
    
    @Test
    void testGetAllTemplates_IOException() throws Exception {
        // 模拟IO异常
        when(templateService.getAllTemplates()).thenThrow(new IOException("文件读取失败"));
        
        // 执行请求
        mockMvc.perform(get("/api/templates"))
                .andExpect(status().isInternalServerError());
        
        verify(templateService, times(1)).getAllTemplates();
    }
    
    @Test
    void testGetTemplatesByType_Success() throws Exception {
        // 准备测试数据
        List<ChecklistTemplate> templates = Arrays.asList(testTemplate);
        when(templateService.getTemplatesByType("code_review")).thenReturn(templates);
        
        // 执行请求
        mockMvc.perform(get("/api/templates/code_review"))
                .andExpect(status().isOk());
        
        verify(templateService, times(1)).getTemplatesByType("code_review");
    }
    
    @Test
    void testGetTemplatesByType_EmptyType() throws Exception {
        // 执行请求 - 空类型
        mockMvc.perform(get("/api/templates/ "))
                .andExpect(status().isBadRequest());
        
        verify(templateService, never()).getTemplatesByType(anyString());
    }
    
    @Test
    void testCreateTemplate_Success() throws Exception {
        // 准备测试数据
        when(templateService.createTemplate(any(ChecklistTemplate.class))).thenReturn(testTemplate);
        
        // 执行请求
        mockMvc.perform(post("/api/templates")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"name\":\"测试模板\",\"type\":\"code_review\"}"))
                .andExpect(status().isCreated());
        
        verify(templateService, times(1)).createTemplate(any(ChecklistTemplate.class));
    }
    
    @Test
    void testCreateTemplate_NullBody() throws Exception {
        // 执行请求 - 空请求体
        mockMvc.perform(post("/api/templates")
                .contentType(MediaType.APPLICATION_JSON)
                .content("null"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, never()).createTemplate(any(ChecklistTemplate.class));
    }
    
    @Test
    void testCreateTemplate_ValidationError() throws Exception {
        // 模拟验证异常
        when(templateService.createTemplate(any(ChecklistTemplate.class)))
                .thenThrow(new IllegalArgumentException("模板名称不能为空"));
        
        // 执行请求
        mockMvc.perform(post("/api/templates")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"name\":\"测试模板\",\"type\":\"code_review\"}"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, times(1)).createTemplate(any(ChecklistTemplate.class));
    }
    
    @Test
    void testUpdateTemplate_Success() throws Exception {
        // 准备测试数据
        when(templateService.updateTemplate(eq("template_001"), any(ChecklistTemplate.class)))
                .thenReturn(testTemplate);
        
        // 执行请求
        mockMvc.perform(put("/api/templates/template_001")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"name\":\"更新模板\",\"type\":\"code_review\"}"))
                .andExpect(status().isOk());
        
        verify(templateService, times(1)).updateTemplate(eq("template_001"), any(ChecklistTemplate.class));
    }
    
    @Test
    void testUpdateTemplate_EmptyId() throws Exception {
        // 执行请求 - 空ID
        mockMvc.perform(put("/api/templates/ ")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"name\":\"更新模板\",\"type\":\"code_review\"}"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, never()).updateTemplate(anyString(), any(ChecklistTemplate.class));
    }
    
    @Test
    void testDeleteTemplate_Success() throws Exception {
        // 模拟删除成功
        doNothing().when(templateService).deleteTemplate("template_001");
        
        // 执行请求
        mockMvc.perform(delete("/api/templates/template_001"))
                .andExpect(status().isOk());
        
        verify(templateService, times(1)).deleteTemplate("template_001");
    }
    
    @Test
    void testDeleteTemplate_NotFound() throws Exception {
        // 模拟模板不存在
        doThrow(new IllegalArgumentException("模板不存在: template_999"))
                .when(templateService).deleteTemplate("template_999");
        
        // 执行请求
        mockMvc.perform(delete("/api/templates/template_999"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, times(1)).deleteTemplate("template_999");
    }
    
    @Test
    void testGetTemplateById_Success() throws Exception {
        // 准备测试数据
        when(templateService.getTemplateById("template_001")).thenReturn(Optional.of(testTemplate));
        
        // 执行请求
        mockMvc.perform(get("/api/templates/detail/template_001"))
                .andExpect(status().isOk());
        
        verify(templateService, times(1)).getTemplateById("template_001");
    }
    
    @Test
    void testGetTemplateById_NotFound() throws Exception {
        // 模拟模板不存在
        when(templateService.getTemplateById("template_999")).thenReturn(Optional.empty());
        
        // 执行请求
        mockMvc.perform(get("/api/templates/detail/template_999"))
                .andExpect(status().isNotFound());
        
        verify(templateService, times(1)).getTemplateById("template_999");
    }
    
    @Test
    void testImportTemplateFromExcel_Success() throws Exception {
        // 创建测试 Excel 文件
        MockMultipartFile excelFile = createTestExcelFile();
        
        // 准备测试数据
        when(templateService.importTemplateFromExcel(any(MultipartFile.class), eq("导入模板"), eq("excel-import")))
                .thenReturn(testTemplate);
        
        // 执行请求
        mockMvc.perform(multipart("/api/templates/import")
                .file(excelFile)
                .param("templateName", "导入模板")
                .param("templateType", "excel-import"))
                .andExpect(status().isCreated());
        
        verify(templateService, times(1)).importTemplateFromExcel(any(MultipartFile.class), eq("导入模板"), eq("excel-import"));
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyFile() throws Exception {
        // 创建空文件
        MockMultipartFile emptyFile = new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", new byte[0]);
        
        // 执行请求
        mockMvc.perform(multipart("/api/templates/import")
                .file(emptyFile)
                .param("templateName", "导入模板")
                .param("templateType", "excel-import"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, never()).importTemplateFromExcel(any(MultipartFile.class), anyString(), anyString());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyTemplateName() throws Exception {
        // 创建测试 Excel 文件
        MockMultipartFile excelFile = createTestExcelFile();
        
        // 执行请求 - 空模板名称
        mockMvc.perform(multipart("/api/templates/import")
                .file(excelFile)
                .param("templateName", "")
                .param("templateType", "excel-import"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, never()).importTemplateFromExcel(any(MultipartFile.class), anyString(), anyString());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyTemplateType() throws Exception {
        // 创建测试 Excel 文件
        MockMultipartFile excelFile = createTestExcelFile();
        
        // 执行请求 - 空模板类型
        mockMvc.perform(multipart("/api/templates/import")
                .file(excelFile)
                .param("templateName", "导入模板")
                .param("templateType", ""))
                .andExpect(status().isBadRequest());
        
        verify(templateService, never()).importTemplateFromExcel(any(MultipartFile.class), anyString(), anyString());
    }
    
    @Test
    void testImportTemplateFromExcel_ValidationError() throws Exception {
        // 创建测试 Excel 文件
        MockMultipartFile excelFile = createTestExcelFile();
        
        // 模拟验证异常
        when(templateService.importTemplateFromExcel(any(MultipartFile.class), eq("导入模板"), eq("excel-import")))
                .thenThrow(new IllegalArgumentException("模板名称已存在"));
        
        // 执行请求
        mockMvc.perform(multipart("/api/templates/import")
                .file(excelFile)
                .param("templateName", "导入模板")
                .param("templateType", "excel-import"))
                .andExpect(status().isBadRequest());
        
        verify(templateService, times(1)).importTemplateFromExcel(any(MultipartFile.class), eq("导入模板"), eq("excel-import"));
    }
    
    @Test
    void testImportTemplateFromExcel_IOException() throws Exception {
        // 创建测试 Excel 文件
        MockMultipartFile excelFile = createTestExcelFile();
        
        // 模拟IO异常
        when(templateService.importTemplateFromExcel(any(MultipartFile.class), eq("导入模板"), eq("excel-import")))
                .thenThrow(new IOException("Excel 文件解析失败"));
        
        // 执行请求
        mockMvc.perform(multipart("/api/templates/import")
                .file(excelFile)
                .param("templateName", "导入模板")
                .param("templateType", "excel-import"))
                .andExpect(status().isInternalServerError());
        
        verify(templateService, times(1)).importTemplateFromExcel(any(MultipartFile.class), eq("导入模板"), eq("excel-import"));
    }
    
    /**
     * 创建测试用的 Excel 文件
     */
    private MockMultipartFile createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("序号");
        headerRow.createCell(1).setCellValue("内容");
        headerRow.createCell(2).setCellValue("分类");
        headerRow.createCell(3).setCellValue("必填");
        
        // 创建数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue(1);
        row1.createCell(1).setCellValue("检查项1");
        row1.createCell(2).setCellValue("分类A");
        row1.createCell(3).setCellValue("是");
        
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue(2);
        row2.createCell(1).setCellValue("检查项2");
        row2.createCell(2).setCellValue("分类B");
        row2.createCell(3).setCellValue("否");
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
}