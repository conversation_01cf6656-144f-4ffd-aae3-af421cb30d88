package com.checklist.controller;

import com.checklist.model.ChecklistReview;
import com.checklist.model.ReviewRecord;
import com.checklist.model.ReviewStatus;
import com.checklist.service.ChecklistReviewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ChecklistReviewControllerTest {
    
    @Mock
    private ChecklistReviewService reviewService;
    
    @InjectMocks
    private ChecklistReviewController reviewController;
    
    private MockMvc mockMvc;
    private ChecklistReview testReview;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(reviewController).build();
        
        // 创建测试评审实例
        testReview = new ChecklistReview();
        testReview.setId("review_001");
        testReview.setTemplateId("template_001");
        testReview.setType("code_review");
        testReview.setStatus("IN_PROGRESS");
    }
    
    @Test
    void testCreateReview_Success() throws Exception {
        // 准备测试数据
        when(reviewService.createReviewFromTemplate("template_001")).thenReturn(testReview);
        
        // 执行请求
        mockMvc.perform(post("/api/reviews")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"templateId\":\"template_001\"}"))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(reviewService, times(1)).createReviewFromTemplate("template_001");
    }
    
    @Test
    void testCreateReview_EmptyTemplateId() throws Exception {
        // 执行请求 - 空模板ID
        mockMvc.perform(post("/api/reviews")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"templateId\":\"\"}"))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, never()).createReviewFromTemplate(anyString());
    }
    
    @Test
    void testCreateReview_ValidationError() throws Exception {
        // 模拟验证异常
        when(reviewService.createReviewFromTemplate("template_999"))
                .thenThrow(new IllegalArgumentException("模板不存在: template_999"));
        
        // 执行请求
        mockMvc.perform(post("/api/reviews")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"templateId\":\"template_999\"}"))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, times(1)).createReviewFromTemplate("template_999");
    }
    
    @Test
    void testGetReviewById_Success() throws Exception {
        // 准备测试数据
        when(reviewService.getReviewById("review_001")).thenReturn(Optional.of(testReview));
        
        // 执行请求
        mockMvc.perform(get("/api/reviews/review_001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(reviewService, times(1)).getReviewById("review_001");
    }
    
    @Test
    void testGetReviewById_NotFound() throws Exception {
        // 模拟评审不存在
        when(reviewService.getReviewById("review_999")).thenReturn(Optional.empty());
        
        // 执行请求
        mockMvc.perform(get("/api/reviews/review_999"))
                .andExpect(status().isNotFound());
        
        verify(reviewService, times(1)).getReviewById("review_999");
    }
    
    @Test
    void testGetReviewById_EmptyId() throws Exception {
        // 执行请求 - 空ID
        mockMvc.perform(get("/api/reviews/ "))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, never()).getReviewById(anyString());
    }
    
    @Test
    void testUpdateReviewItemStatus_Success() throws Exception {
        // 准备测试数据
        when(reviewService.updateReviewItemStatus(
                eq("review_001"), eq("item_001"), eq(ReviewStatus.PASS), 
                eq("测试通过"), eq("reviewer1")))
                .thenReturn(testReview);
        
        // 执行请求
        mockMvc.perform(put("/api/reviews/review_001/items/item_001")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"status\":\"PASS\",\"comment\":\"测试通过\",\"reviewer\":\"reviewer1\"}"))
                .andExpect(status().isOk());
        
        verify(reviewService, times(1)).updateReviewItemStatus(
                eq("review_001"), eq("item_001"), eq(ReviewStatus.PASS), 
                eq("测试通过"), eq("reviewer1"));
    }
    
    @Test
    void testUpdateReviewItemStatus_EmptyStatus() throws Exception {
        // 执行请求 - 空状态
        mockMvc.perform(put("/api/reviews/review_001/items/item_001")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"status\":\"\",\"comment\":\"测试\",\"reviewer\":\"reviewer1\"}"))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, never()).updateReviewItemStatus(
                anyString(), anyString(), any(ReviewStatus.class), anyString(), anyString());
    }
    
    @Test
    void testUpdateReviewItemStatus_InvalidStatus() throws Exception {
        // 执行请求 - 无效状态
        mockMvc.perform(put("/api/reviews/review_001/items/item_001")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"status\":\"INVALID\",\"comment\":\"测试\",\"reviewer\":\"reviewer1\"}"))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, never()).updateReviewItemStatus(
                anyString(), anyString(), any(ReviewStatus.class), anyString(), anyString());
    }
    
    @Test
    void testBatchUpdateReviewItemStatus_Success() throws Exception {
        // 准备测试数据
        List<String> itemIds = Arrays.asList("item_001", "item_002");
        when(reviewService.batchUpdateReviewItemStatus(
                eq("review_001"), eq(itemIds), eq(ReviewStatus.PASS), 
                eq("批量通过"), eq("reviewer1")))
                .thenReturn(testReview);
        
        // 执行请求
        mockMvc.perform(put("/api/reviews/review_001/items/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"itemIds\":[\"item_001\",\"item_002\"],\"status\":\"PASS\",\"comment\":\"批量通过\",\"reviewer\":\"reviewer1\"}"))
                .andExpect(status().isOk());
        
        verify(reviewService, times(1)).batchUpdateReviewItemStatus(
                eq("review_001"), eq(itemIds), eq(ReviewStatus.PASS), 
                eq("批量通过"), eq("reviewer1"));
    }
    
    @Test
    void testBatchUpdateReviewItemStatus_EmptyItemIds() throws Exception {
        // 执行请求 - 空项目ID列表
        mockMvc.perform(put("/api/reviews/review_001/items/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"itemIds\":[],\"status\":\"PASS\",\"comment\":\"批量通过\",\"reviewer\":\"reviewer1\"}"))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, never()).batchUpdateReviewItemStatus(
                anyString(), anyList(), any(ReviewStatus.class), anyString(), anyString());
    }
    
    @Test
    void testGetReviewHistory_Success() throws Exception {
        // 准备测试数据
        List<ReviewRecord> history = Arrays.asList(new ReviewRecord());
        when(reviewService.getReviewHistory("review_001")).thenReturn(history);
        
        // 执行请求
        mockMvc.perform(get("/api/reviews/review_001/history"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(reviewService, times(1)).getReviewHistory("review_001");
    }
    
    @Test
    void testGetReviewHistory_EmptyId() throws Exception {
        // 执行请求 - 空ID
        mockMvc.perform(get("/api/reviews/ /history"))
                .andExpect(status().isBadRequest());
        
        verify(reviewService, never()).getReviewHistory(anyString());
    }
    
    @Test
    void testGetReviews_WithType() throws Exception {
        // 准备测试数据
        List<ChecklistReview> reviews = Arrays.asList(testReview);
        when(reviewService.getReviewsByType("code_review")).thenReturn(reviews);
        
        // 执行请求
        mockMvc.perform(get("/api/reviews?type=code_review"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(reviewService, times(1)).getReviewsByType("code_review");
        verify(reviewService, never()).getRecentReviews(anyInt());
    }
    
    @Test
    void testGetReviews_WithoutType() throws Exception {
        // 准备测试数据
        List<ChecklistReview> reviews = Arrays.asList(testReview);
        when(reviewService.getRecentReviews(50)).thenReturn(reviews);
        
        // 执行请求
        mockMvc.perform(get("/api/reviews"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(reviewService, times(1)).getRecentReviews(50);
        verify(reviewService, never()).getReviewsByType(anyString());
    }
    
    @Test
    void testGetReviewStatistics_Success() throws Exception {
        // 准备测试数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalReviews", 10);
        when(reviewService.getReviewStatistics()).thenReturn(statistics);
        
        // 执行请求
        mockMvc.perform(get("/api/reviews/statistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        
        verify(reviewService, times(1)).getReviewStatistics();
    }
    
    @Test
    void testGetReviewStatistics_IOException() throws Exception {
        // 模拟IO异常
        when(reviewService.getReviewStatistics()).thenThrow(new IOException("读取统计信息失败"));
        
        // 执行请求
        mockMvc.perform(get("/api/reviews/statistics"))
                .andExpect(status().isInternalServerError());
        
        verify(reviewService, times(1)).getReviewStatistics();
    }
}