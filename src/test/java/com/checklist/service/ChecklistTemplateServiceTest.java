package com.checklist.service;

import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistItem;
import com.checklist.repository.ChecklistTemplateRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistTemplateService 单元测试
 */
class ChecklistTemplateServiceTest {
    
    private ChecklistTemplateService templateService;
    private ChecklistTemplateRepository templateRepository;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() throws IOException {
        // 使用临时目录创建测试用的repository
        String testDataDir = tempDir.resolve("templates").toString();
        templateRepository = new ChecklistTemplateRepository(testDataDir);
        ExcelImportService excelImportService = new ExcelImportService();
        templateService = new ChecklistTemplateService(templateRepository, excelImportService);
    }
    
    @Test
    void testCreateTemplate_Success() throws IOException {
        // 准备测试数据
        ChecklistTemplate template = createValidTemplate();
        
        // 执行创建操作
        ChecklistTemplate result = templateService.createTemplate(template);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("测试模板", result.getName());
        assertEquals("code-review", result.getType());
        assertEquals("1.0", result.getVersion());
        assertNotNull(result.getCreatedTime());
        assertNotNull(result.getUpdatedTime());
        assertEquals(2, result.getItems().size());
        
        // 验证检查项ID已生成
        for (ChecklistItem item : result.getItems()) {
            assertNotNull(item.getId());
            assertNotNull(item.getRequired());
        }
    }
    
    @Test
    void testCreateTemplate_NullTemplate() {
        // 测试空模板
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(null)
        );
        assertEquals("模板对象不能为空", exception.getMessage());
    }
    
    @Test
    void testCreateTemplate_EmptyName() {
        // 测试空名称
        ChecklistTemplate template = createValidTemplate();
        template.setName("");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(template)
        );
        assertEquals("模板名称不能为空", exception.getMessage());
    }
    
    @Test
    void testCreateTemplate_EmptyType() {
        // 测试空类型
        ChecklistTemplate template = createValidTemplate();
        template.setType("");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(template)
        );
        assertEquals("模板类型不能为空", exception.getMessage());
    }
    
    @Test
    void testCreateTemplate_EmptyItems() {
        // 测试空检查项列表
        ChecklistTemplate template = createValidTemplate();
        template.setItems(Arrays.asList());
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(template)
        );
        assertEquals("模板必须包含至少一个检查项", exception.getMessage());
    }
    
    @Test
    void testCreateTemplate_DuplicateName() throws IOException {
        // 先创建一个模板
        ChecklistTemplate template1 = createValidTemplate();
        templateService.createTemplate(template1);
        
        // 尝试创建同名模板
        ChecklistTemplate template2 = createValidTemplate();
        template2.setType("unit-test");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(template2)
        );
        assertEquals("模板名称已存在: 测试模板", exception.getMessage());
    }
    
    @Test
    void testUpdateTemplate_Success() throws IOException {
        // 先创建一个模板
        ChecklistTemplate original = templateService.createTemplate(createValidTemplate());
        String templateId = original.getId();
        LocalDateTime originalCreatedTime = original.getCreatedTime();
        
        // 准备更新数据
        ChecklistTemplate updated = createValidTemplate();
        updated.setName("更新后的模板");
        updated.setType("integration-test");
        
        // 执行更新操作
        ChecklistTemplate result = templateService.updateTemplate(templateId, updated);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        assertEquals("更新后的模板", result.getName());
        assertEquals("integration-test", result.getType());
        // JSON serialization loses nanosecond precision, so compare truncated to seconds
        assertEquals(originalCreatedTime.withNano(0), result.getCreatedTime().withNano(0));
        assertFalse(result.getUpdatedTime().isBefore(originalCreatedTime));
    }
    
    @Test
    void testUpdateTemplate_NotFound() {
        ChecklistTemplate template = createValidTemplate();
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.updateTemplate("non-existent-id", template)
        );
        assertEquals("模板不存在: non-existent-id", exception.getMessage());
    }
    
    @Test
    void testUpdateTemplate_DuplicateName() throws IOException {
        // 创建两个模板
        ChecklistTemplate template1 = createValidTemplate();
        template1.setName("模板1");
        ChecklistTemplate saved1 = templateService.createTemplate(template1);
        
        ChecklistTemplate template2 = createValidTemplate();
        template2.setName("模板2");
        template2.setType("unit-test");
        ChecklistTemplate saved2 = templateService.createTemplate(template2);
        
        // 尝试将模板2的名称更新为模板1的名称
        template2.setName("模板1");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.updateTemplate(saved2.getId(), template2)
        );
        assertEquals("模板名称已存在: 模板1", exception.getMessage());
    }
    
    @Test
    void testDeleteTemplate_Success() throws IOException {
        // 先创建一个模板
        ChecklistTemplate template = templateService.createTemplate(createValidTemplate());
        String templateId = template.getId();
        
        // 验证模板存在
        assertTrue(templateService.getTemplateById(templateId).isPresent());
        
        // 执行删除操作
        templateService.deleteTemplate(templateId);
        
        // 验证模板已删除
        assertFalse(templateService.getTemplateById(templateId).isPresent());
    }
    
    @Test
    void testDeleteTemplate_NotFound() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.deleteTemplate("non-existent-id")
        );
        assertEquals("模板不存在: non-existent-id", exception.getMessage());
    }
    
    @Test
    void testGetTemplateById_Success() throws IOException {
        // 创建模板
        ChecklistTemplate template = templateService.createTemplate(createValidTemplate());
        String templateId = template.getId();
        
        // 查询模板
        Optional<ChecklistTemplate> result = templateService.getTemplateById(templateId);
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(templateId, result.get().getId());
        assertEquals("测试模板", result.get().getName());
    }
    
    @Test
    void testGetTemplateById_NotFound() throws IOException {
        Optional<ChecklistTemplate> result = templateService.getTemplateById("non-existent-id");
        assertFalse(result.isPresent());
    }
    
    @Test
    void testGetAllTemplates() throws IOException {
        // 创建多个模板
        ChecklistTemplate template1 = createValidTemplate();
        template1.setName("模板1");
        templateService.createTemplate(template1);
        
        ChecklistTemplate template2 = createValidTemplate();
        template2.setName("模板2");
        template2.setType("unit-test");
        templateService.createTemplate(template2);
        
        // 查询所有模板
        List<ChecklistTemplate> result = templateService.getAllTemplates();
        
        // 验证结果
        assertEquals(2, result.size());
    }
    
    @Test
    void testGetTemplatesByType() throws IOException {
        // 创建不同类型的模板
        ChecklistTemplate template1 = createValidTemplate();
        template1.setName("模板1");
        template1.setType("code-review");
        templateService.createTemplate(template1);
        
        ChecklistTemplate template2 = createValidTemplate();
        template2.setName("模板2");
        template2.setType("unit-test");
        templateService.createTemplate(template2);
        
        ChecklistTemplate template3 = createValidTemplate();
        template3.setName("模板3");
        template3.setType("code-review");
        templateService.createTemplate(template3);
        
        // 查询特定类型的模板
        List<ChecklistTemplate> result = templateService.getTemplatesByType("code-review");
        
        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(t -> "code-review".equals(t.getType())));
    }
    
    @Test
    void testGetTemplateByName() throws IOException {
        // 创建模板
        ChecklistTemplate template = createValidTemplate();
        templateService.createTemplate(template);
        
        // 根据名称查询
        Optional<ChecklistTemplate> result = templateService.getTemplateByName("测试模板");
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("测试模板", result.get().getName());
    }
    
    @Test
    void testGetLatestTemplateByType() throws IOException {
        // 创建同类型的多个版本模板
        ChecklistTemplate template1 = createValidTemplate();
        template1.setName("模板1");
        template1.setType("code-review");
        template1.setVersion("1.0");
        ChecklistTemplate saved1 = templateService.createTemplate(template1);
        
        // 稍等一下确保时间不同
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        ChecklistTemplate template2 = createValidTemplate();
        template2.setName("模板2");
        template2.setType("code-review");
        template2.setVersion("2.0");
        ChecklistTemplate saved2 = templateService.createTemplate(template2);
        
        // 查询最新版本
        Optional<ChecklistTemplate> result = templateService.getLatestTemplateByType("code-review");
        
        // 验证结果（应该返回最后更新的模板）
        assertTrue(result.isPresent());
        // 由于时间可能相同，只验证返回的是其中一个模板
        assertTrue(result.get().getId().equals(saved1.getId()) || result.get().getId().equals(saved2.getId()));
        assertEquals("code-review", result.get().getType());
    }
    
    @Test
    void testGetTemplateByTypeAndVersion() throws IOException {
        // 创建模板
        ChecklistTemplate template = createValidTemplate();
        template.setType("code-review");
        template.setVersion("1.5");
        templateService.createTemplate(template);
        
        // 根据类型和版本查询
        Optional<ChecklistTemplate> result = templateService.getTemplateByTypeAndVersion("code-review", "1.5");
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("code-review", result.get().getType());
        assertEquals("1.5", result.get().getVersion());
    }
    
    @Test
    void testValidateChecklistItem_EmptyContent() {
        ChecklistTemplate template = createValidTemplate();
        template.getItems().get(0).setContent("");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(template)
        );
        assertEquals("检查项内容不能为空", exception.getMessage());
    }
    
    @Test
    void testValidateChecklistItem_InvalidSequence() {
        ChecklistTemplate template = createValidTemplate();
        template.getItems().get(0).setSequence(0);
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> templateService.createTemplate(template)
        );
        assertEquals("检查项序号必须为正整数", exception.getMessage());
    }
    
    @Test
    void testFormatTemplateItems() throws IOException {
        // 创建没有ID的检查项
        ChecklistTemplate template = createValidTemplate();
        template.getItems().get(0).setId(null);
        template.getItems().get(1).setId("");
        template.getItems().get(0).setRequired(null);
        
        // 创建模板
        ChecklistTemplate result = templateService.createTemplate(template);
        
        // 验证ID已生成，required已设置默认值
        for (ChecklistItem item : result.getItems()) {
            assertNotNull(item.getId());
            assertFalse(item.getId().isEmpty());
            assertNotNull(item.getRequired());
        }
    }
    
    @Test
    void testImportTemplateFromExcel_Success() throws IOException {
        // 创建测试 Excel 文件
        MultipartFile excelFile = createTestExcelFile();
        
        // 执行导入
        ChecklistTemplate result = templateService.importTemplateFromExcel(
            excelFile, "导入模板", "excel-import");
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("导入模板", result.getName());
        assertEquals("excel-import", result.getType());
        assertEquals("1.0", result.getVersion());
        assertNotNull(result.getCreatedTime());
        assertNotNull(result.getUpdatedTime());
        
        // 验证检查项
        List<ChecklistItem> items = result.getItems();
        assertNotNull(items);
        assertEquals(2, items.size());
        
        ChecklistItem item1 = items.get(0);
        assertEquals(1, item1.getSequence());
        assertEquals("检查项1", item1.getContent());
        assertEquals("分类A", item1.getCategory());
        assertTrue(item1.getRequired());
        
        ChecklistItem item2 = items.get(1);
        assertEquals(2, item2.getSequence());
        assertEquals("检查项2", item2.getContent());
        assertEquals("分类B", item2.getCategory());
        assertFalse(item2.getRequired());
    }
    
    @Test
    void testImportTemplateFromExcel_DuplicateName() throws IOException {
        // 先创建一个模板
        ChecklistTemplate existingTemplate = createValidTemplate();
        existingTemplate.setName("重复名称模板");
        templateService.createTemplate(existingTemplate);
        
        // 创建测试 Excel 文件
        MultipartFile excelFile = createTestExcelFile();
        
        // 尝试导入同名模板
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            templateService.importTemplateFromExcel(excelFile, "重复名称模板", "excel-import");
        });
        assertEquals("模板名称已存在: 重复名称模板", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_NullFile() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            templateService.importTemplateFromExcel(null, "测试模板", "测试类型");
        });
        assertEquals("Excel 文件不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyTemplateName() throws IOException {
        MultipartFile excelFile = createTestExcelFile();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            templateService.importTemplateFromExcel(excelFile, "", "测试类型");
        });
        assertEquals("模板名称不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyTemplateType() throws IOException {
        MultipartFile excelFile = createTestExcelFile();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            templateService.importTemplateFromExcel(excelFile, "测试模板", "");
        });
        assertEquals("模板类型不能为空", exception.getMessage());
    }
    
    /**
     * 创建测试用的 Excel 文件
     */
    private MultipartFile createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("序号");
        headerRow.createCell(1).setCellValue("内容");
        headerRow.createCell(2).setCellValue("分类");
        headerRow.createCell(3).setCellValue("必填");
        
        // 创建数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue(1);
        row1.createCell(1).setCellValue("检查项1");
        row1.createCell(2).setCellValue("分类A");
        row1.createCell(3).setCellValue("是");
        
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue(2);
        row2.createCell(1).setCellValue("检查项2");
        row2.createCell(2).setCellValue("分类B");
        row2.createCell(3).setCellValue("否");
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
    
    /**
     * 创建有效的测试模板
     */
    private ChecklistTemplate createValidTemplate() {
        ChecklistTemplate template = new ChecklistTemplate();
        template.setName("测试模板");
        template.setType("code-review");
        template.setVersion("1.0");
        
        ChecklistItem item1 = new ChecklistItem();
        item1.setSequence(1);
        item1.setContent("检查代码规范");
        item1.setRequired(true);
        item1.setCategory("代码质量");
        
        ChecklistItem item2 = new ChecklistItem();
        item2.setSequence(2);
        item2.setContent("检查单元测试覆盖率");
        item2.setRequired(false);
        item2.setCategory("测试");
        
        template.setItems(Arrays.asList(item1, item2));
        
        return template;
    }
}