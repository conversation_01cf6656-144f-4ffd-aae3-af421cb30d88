package com.checklist.service;

import com.checklist.model.*;
import com.checklist.repository.ChecklistReviewRepository;
import com.checklist.repository.ChecklistTemplateRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ChecklistReviewService 单元测试
 */
class ChecklistReviewServiceTest {
    
    private ChecklistReviewService reviewService;
    private ChecklistReviewRepository reviewRepository;
    private ChecklistTemplateRepository templateRepository;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() throws IOException {
        // 使用临时目录创建测试用的repository
        String testReviewDir = tempDir.resolve("reviews").toString();
        String testTemplateDir = tempDir.resolve("templates").toString();
        
        reviewRepository = new ChecklistReviewRepository(testReviewDir);
        templateRepository = new ChecklistTemplateRepository(testTemplateDir);
        reviewService = new ChecklistReviewService(reviewRepository, templateRepository);
    }
    
    @Test
    void testCreateReviewFromTemplate_Success() throws IOException {
        // 先创建一个模板
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        
        // 基于模板创建评审实例
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        // 验证结果
        assertNotNull(review);
        assertNotNull(review.getId());
        assertEquals(savedTemplate.getId(), review.getTemplateId());
        assertEquals(savedTemplate.getVersion(), review.getTemplateVersion());
        assertEquals(savedTemplate.getType(), review.getType());
        assertEquals("IN_PROGRESS", review.getStatus());
        assertNotNull(review.getCreatedTime());
        assertNotNull(review.getReviewItems());
        assertEquals(2, review.getReviewItems().size());
        
        // 验证评审项
        for (ReviewItem item : review.getReviewItems()) {
            assertNotNull(item.getItemId());
            assertEquals(ReviewStatus.PENDING, item.getStatus());
            assertEquals("", item.getComment());
            assertNotNull(item.getReviewHistory());
            assertTrue(item.getReviewHistory().isEmpty());
        }
    }
    
    @Test
    void testCreateReviewFromTemplate_TemplateNotFound() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> reviewService.createReviewFromTemplate("non-existent-template")
        );
        assertEquals("模板不存在: non-existent-template", exception.getMessage());
    }
    
    @Test
    void testCreateReviewFromTemplate_EmptyTemplateId() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> reviewService.createReviewFromTemplate("")
        );
        assertEquals("模板ID不能为空", exception.getMessage());
    }
    
    @Test
    void testUpdateReviewItemStatus_Success() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        String itemId = review.getReviewItems().get(0).getItemId();
        
        // 更新评审项状态
        ChecklistReview updatedReview = reviewService.updateReviewItemStatus(
            review.getId(), itemId, ReviewStatus.PASS, "测试通过", "测试员"
        );
        
        // 验证结果
        assertNotNull(updatedReview);
        ReviewItem updatedItem = findReviewItemById(updatedReview, itemId);
        assertNotNull(updatedItem);
        assertEquals(ReviewStatus.PASS, updatedItem.getStatus());
        assertEquals("测试通过", updatedItem.getComment());
        assertEquals(1, updatedItem.getReviewHistory().size());
        
        ReviewRecord record = updatedItem.getReviewHistory().get(0);
        assertEquals("测试员", record.getReviewer());
        assertEquals(ReviewStatus.PASS, record.getStatus());
        assertEquals("测试通过", record.getComment());
        assertNotNull(record.getReviewTime());
    }
    
    @Test
    void testUpdateReviewItemStatus_ReviewNotFound() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> reviewService.updateReviewItemStatus(
                "non-existent-review", "item-id", ReviewStatus.PASS, "comment", "reviewer"
            )
        );
        assertEquals("评审实例不存在: non-existent-review", exception.getMessage());
    }
    
    @Test
    void testUpdateReviewItemStatus_ItemNotFound() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> reviewService.updateReviewItemStatus(
                review.getId(), "non-existent-item", ReviewStatus.PASS, "comment", "reviewer"
            )
        );
        assertEquals("评审项不存在: non-existent-item", exception.getMessage());
    }
    
    @Test
    void testBatchUpdateReviewItemStatus_Success() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        List<String> itemIds = Arrays.asList(
            review.getReviewItems().get(0).getItemId(),
            review.getReviewItems().get(1).getItemId()
        );
        
        // 批量更新评审项状态
        ChecklistReview updatedReview = reviewService.batchUpdateReviewItemStatus(
            review.getId(), itemIds, ReviewStatus.PASS, "批量通过", "测试员"
        );
        
        // 验证结果
        assertNotNull(updatedReview);
        for (String itemId : itemIds) {
            ReviewItem item = findReviewItemById(updatedReview, itemId);
            assertNotNull(item);
            assertEquals(ReviewStatus.PASS, item.getStatus());
            assertEquals("批量通过", item.getComment());
            assertEquals(1, item.getReviewHistory().size());
            
            ReviewRecord record = item.getReviewHistory().get(0);
            assertEquals("测试员", record.getReviewer());
            assertEquals(ReviewStatus.PASS, record.getStatus());
            assertEquals("批量通过", record.getComment());
        }
        
        // 验证整体状态
        assertEquals("COMPLETED", updatedReview.getStatus());
    }
    
    @Test
    void testBatchUpdateReviewItemStatus_EmptyItemIds() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> reviewService.batchUpdateReviewItemStatus(
                "review-id", new ArrayList<>(), ReviewStatus.PASS, "comment", "reviewer"
            )
        );
        assertEquals("评审项ID列表不能为空", exception.getMessage());
    }
    
    @Test
    void testGetReviewById_Success() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        // 查询评审实例
        Optional<ChecklistReview> result = reviewService.getReviewById(review.getId());
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(review.getId(), result.get().getId());
        assertEquals(review.getTemplateId(), result.get().getTemplateId());
    }
    
    @Test
    void testGetReviewById_NotFound() throws IOException {
        Optional<ChecklistReview> result = reviewService.getReviewById("non-existent-id");
        assertFalse(result.isPresent());
    }
    
    @Test
    void testGetReviewHistory_Success() throws IOException {
        // 创建评审实例并更新状态
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        String itemId1 = review.getReviewItems().get(0).getItemId();
        String itemId2 = review.getReviewItems().get(1).getItemId();
        
        // 更新多个评审项
        reviewService.updateReviewItemStatus(review.getId(), itemId1, ReviewStatus.PASS, "第一项通过", "评审员1");
        reviewService.updateReviewItemStatus(review.getId(), itemId2, ReviewStatus.FAIL, "第二项不通过", "评审员2");
        
        // 获取评审历史
        List<ReviewRecord> history = reviewService.getReviewHistory(review.getId());
        
        // 验证结果
        assertEquals(2, history.size());
        // 历史记录按时间倒序排列
        assertTrue(history.get(0).getReviewTime().isAfter(history.get(1).getReviewTime()) ||
                  history.get(0).getReviewTime().equals(history.get(1).getReviewTime()));
    }
    
    @Test
    void testGetReviewItemHistory_Success() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        String itemId = review.getReviewItems().get(0).getItemId();
        
        // 多次更新同一个评审项
        reviewService.updateReviewItemStatus(review.getId(), itemId, ReviewStatus.FAIL, "第一次不通过", "评审员1");
        
        // 稍等一下确保时间不同
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        reviewService.updateReviewItemStatus(review.getId(), itemId, ReviewStatus.PASS, "修改后通过", "评审员2");
        
        // 获取评审项历史
        List<ReviewRecord> history = reviewService.getReviewItemHistory(review.getId(), itemId);
        
        // 验证结果
        assertEquals(2, history.size());
        // 验证包含两个不同的评审员
        Set<String> reviewers = new HashSet<>();
        Set<ReviewStatus> statuses = new HashSet<>();
        for (ReviewRecord record : history) {
            reviewers.add(record.getReviewer());
            statuses.add(record.getStatus());
        }
        assertTrue(reviewers.contains("评审员1"));
        assertTrue(reviewers.contains("评审员2"));
        assertTrue(statuses.contains(ReviewStatus.FAIL));
        assertTrue(statuses.contains(ReviewStatus.PASS));
    }
    
    @Test
    void testGetReviewsByType_Success() throws IOException {
        // 创建不同类型的评审实例
        ChecklistTemplate template1 = createValidTemplate();
        template1.setType("code-review");
        ChecklistTemplate savedTemplate1 = templateRepository.save(template1);
        
        ChecklistTemplate template2 = createValidTemplate();
        template2.setName("单元测试模板");
        template2.setType("unit-test");
        ChecklistTemplate savedTemplate2 = templateRepository.save(template2);
        
        reviewService.createReviewFromTemplate(savedTemplate1.getId());
        reviewService.createReviewFromTemplate(savedTemplate2.getId());
        reviewService.createReviewFromTemplate(savedTemplate1.getId()); // 再创建一个code-review
        
        // 查询特定类型的评审
        List<ChecklistReview> codeReviews = reviewService.getReviewsByType("code-review");
        List<ChecklistReview> unitTestReviews = reviewService.getReviewsByType("unit-test");
        
        // 验证结果
        assertEquals(2, codeReviews.size());
        assertEquals(1, unitTestReviews.size());
        assertTrue(codeReviews.stream().allMatch(r -> "code-review".equals(r.getType())));
        assertTrue(unitTestReviews.stream().allMatch(r -> "unit-test".equals(r.getType())));
    }
    
    @Test
    void testGetReviewStatistics_Success() throws IOException {
        // 创建多个评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        
        ChecklistReview review1 = reviewService.createReviewFromTemplate(savedTemplate.getId());
        ChecklistReview review2 = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        // 更新一些状态
        String itemId1 = review1.getReviewItems().get(0).getItemId();
        reviewService.updateReviewItemStatus(review1.getId(), itemId1, ReviewStatus.PASS, "通过", "评审员");
        
        // 获取统计信息
        Map<String, Object> statistics = reviewService.getReviewStatistics();
        
        // 验证结果
        assertNotNull(statistics);
        assertEquals(2, statistics.get("totalReviews"));
        assertNotNull(statistics.get("statusCounts"));
        assertNotNull(statistics.get("typeCounts"));
        assertNotNull(statistics.get("todayReviews"));
        assertEquals(2L, statistics.get("todayReviews"));
    }
    
    @Test
    void testDeleteReview_Success() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        // 验证评审存在
        assertTrue(reviewService.getReviewById(review.getId()).isPresent());
        
        // 删除评审
        reviewService.deleteReview(review.getId());
        
        // 验证评审已删除
        assertFalse(reviewService.getReviewById(review.getId()).isPresent());
    }
    
    @Test
    void testDeleteReview_NotFound() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> reviewService.deleteReview("non-existent-id")
        );
        assertEquals("评审实例不存在: non-existent-id", exception.getMessage());
    }
    
    @Test
    void testCheckReviewConflicts_HasConflicts() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        String itemId = review.getReviewItems().get(0).getItemId();
        
        // 创建冲突的评审记录
        reviewService.updateReviewItemStatus(review.getId(), itemId, ReviewStatus.PASS, "通过", "评审员1");
        reviewService.updateReviewItemStatus(review.getId(), itemId, ReviewStatus.FAIL, "不通过", "评审员2");
        
        // 检查冲突
        List<ReviewRecord> conflicts = reviewService.checkReviewConflicts(review.getId(), itemId);
        
        // 验证结果
        assertEquals(2, conflicts.size());
        Set<ReviewStatus> statuses = new HashSet<>();
        for (ReviewRecord record : conflicts) {
            statuses.add(record.getStatus());
        }
        assertTrue(statuses.contains(ReviewStatus.PASS));
        assertTrue(statuses.contains(ReviewStatus.FAIL));
    }
    
    @Test
    void testCheckReviewConflicts_NoConflicts() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        String itemId = review.getReviewItems().get(0).getItemId();
        
        // 创建一致的评审记录
        reviewService.updateReviewItemStatus(review.getId(), itemId, ReviewStatus.PASS, "通过", "评审员1");
        reviewService.updateReviewItemStatus(review.getId(), itemId, ReviewStatus.PASS, "确认通过", "评审员2");
        
        // 检查冲突
        List<ReviewRecord> conflicts = reviewService.checkReviewConflicts(review.getId(), itemId);
        
        // 验证结果
        assertTrue(conflicts.isEmpty());
    }
    
    @Test
    void testOverallStatusUpdate_AllPass() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        // 所有项都通过
        List<String> allItemIds = review.getReviewItems().stream()
            .map(ReviewItem::getItemId)
            .collect(java.util.stream.Collectors.toList());
        
        ChecklistReview updatedReview = reviewService.batchUpdateReviewItemStatus(
            review.getId(), allItemIds, ReviewStatus.PASS, "全部通过", "评审员"
        );
        
        // 验证整体状态
        assertEquals("COMPLETED", updatedReview.getStatus());
    }
    
    @Test
    void testOverallStatusUpdate_HasFailed() throws IOException {
        // 创建评审实例
        ChecklistTemplate template = createValidTemplate();
        ChecklistTemplate savedTemplate = templateRepository.save(template);
        ChecklistReview review = reviewService.createReviewFromTemplate(savedTemplate.getId());
        
        String itemId1 = review.getReviewItems().get(0).getItemId();
        String itemId2 = review.getReviewItems().get(1).getItemId();
        
        // 一个通过，一个不通过
        reviewService.updateReviewItemStatus(review.getId(), itemId1, ReviewStatus.PASS, "通过", "评审员");
        ChecklistReview updatedReview = reviewService.updateReviewItemStatus(
            review.getId(), itemId2, ReviewStatus.FAIL, "不通过", "评审员"
        );
        
        // 验证整体状态
        assertEquals("FAILED", updatedReview.getStatus());
    }
    
    /**
     * 创建有效的测试模板
     */
    private ChecklistTemplate createValidTemplate() {
        ChecklistTemplate template = new ChecklistTemplate();
        template.setName("测试模板");
        template.setType("code-review");
        template.setVersion("1.0");
        template.setCreatedTime(LocalDateTime.now());
        template.setUpdatedTime(LocalDateTime.now());
        
        ChecklistItem item1 = new ChecklistItem();
        item1.setId("item1");
        item1.setSequence(1);
        item1.setContent("检查代码规范");
        item1.setRequired(true);
        item1.setCategory("代码质量");
        
        ChecklistItem item2 = new ChecklistItem();
        item2.setId("item2");
        item2.setSequence(2);
        item2.setContent("检查单元测试覆盖率");
        item2.setRequired(false);
        item2.setCategory("测试");
        
        template.setItems(Arrays.asList(item1, item2));
        
        return template;
    }
    
    /**
     * 根据ID查找评审项
     */
    private ReviewItem findReviewItemById(ChecklistReview review, String itemId) {
        if (review.getReviewItems() != null) {
            for (ReviewItem item : review.getReviewItems()) {
                if (itemId.equals(item.getItemId())) {
                    return item;
                }
            }
        }
        return null;
    }
}