package com.checklist.service;

import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistItem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel 导入服务测试类
 */
class ExcelImportServiceTest {
    
    private ExcelImportService excelImportService;
    
    @BeforeEach
    void setUp() {
        excelImportService = new ExcelImportService();
    }
    
    @Test
    void testImportTemplateFromExcel_Success() throws IOException {
        // 创建测试 Excel 文件
        MultipartFile excelFile = createTestExcelFile();
        
        // 执行导入
        ChecklistTemplate template = excelImportService.importTemplateFromExcel(
            excelFile, "测试模板", "测试类型");
        
        // 验证结果
        assertNotNull(template);
        assertEquals("测试模板", template.getName());
        assertEquals("测试类型", template.getType());
        assertEquals("1.0", template.getVersion());
        assertNotNull(template.getId());
        assertNotNull(template.getCreatedTime());
        assertNotNull(template.getUpdatedTime());
        
        // 验证检查项
        List<ChecklistItem> items = template.getItems();
        assertNotNull(items);
        assertEquals(3, items.size());
        
        ChecklistItem item1 = items.get(0);
        assertEquals(1, item1.getSequence());
        assertEquals("检查项1", item1.getContent());
        assertEquals("分类A", item1.getCategory());
        assertTrue(item1.getRequired());
        
        ChecklistItem item2 = items.get(1);
        assertEquals(2, item2.getSequence());
        assertEquals("检查项2", item2.getContent());
        assertEquals("分类B", item2.getCategory());
        assertFalse(item2.getRequired());
        
        ChecklistItem item3 = items.get(2);
        assertEquals(3, item3.getSequence());
        assertEquals("检查项3", item3.getContent());
        assertNull(item3.getCategory());
        assertFalse(item3.getRequired());
    }
    
    @Test
    void testImportTemplateFromExcel_NullFile() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(null, "测试模板", "测试类型");
        });
        assertEquals("Excel 文件不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyFile() {
        MultipartFile emptyFile = new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", new byte[0]);
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(emptyFile, "测试模板", "测试类型");
        });
        assertEquals("Excel 文件不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_NullTemplateName() throws IOException {
        MultipartFile excelFile = createTestExcelFile();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, null, "测试类型");
        });
        assertEquals("模板名称不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyTemplateName() throws IOException {
        MultipartFile excelFile = createTestExcelFile();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, "  ", "测试类型");
        });
        assertEquals("模板名称不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_NullTemplateType() throws IOException {
        MultipartFile excelFile = createTestExcelFile();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, "测试模板", null);
        });
        assertEquals("模板类型不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyTemplateType() throws IOException {
        MultipartFile excelFile = createTestExcelFile();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, "测试模板", "  ");
        });
        assertEquals("模板类型不能为空", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_InvalidFileFormat() {
        MultipartFile invalidFile = new MockMultipartFile("file", "test.txt", 
            "text/plain", "invalid content".getBytes());
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(invalidFile, "测试模板", "测试类型");
        });
        assertEquals("文件格式不支持，请上传 .xlsx 或 .xls 格式的 Excel 文件", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_LargeFile() {
        // 创建超过 10MB 的文件
        byte[] largeContent = new byte[11 * 1024 * 1024]; // 11MB
        MultipartFile largeFile = new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", largeContent);
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(largeFile, "测试模板", "测试类型");
        });
        assertEquals("文件大小不能超过 10MB", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_NoHeaderRow() throws IOException {
        // 创建没有表头的 Excel 文件
        MultipartFile excelFile = createExcelFileWithoutHeader();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, "测试模板", "测试类型");
        });
        assertEquals("Excel 文件格式不正确，未找到表头行", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_NoContentColumn() throws IOException {
        // 创建没有内容列的 Excel 文件
        MultipartFile excelFile = createExcelFileWithoutContentColumn();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, "测试模板", "测试类型");
        });
        assertEquals("Excel 文件中未找到内容列，请确保表头包含'内容'或'检查项'列", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_EmptyContent() throws IOException {
        // 创建没有有效数据的 Excel 文件
        MultipartFile excelFile = createExcelFileWithEmptyContent();
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            excelImportService.importTemplateFromExcel(excelFile, "测试模板", "测试类型");
        });
        assertEquals("Excel 文件中没有找到有效的检查项数据", exception.getMessage());
    }
    
    @Test
    void testImportTemplateFromExcel_WithEnglishHeaders() throws IOException {
        // 创建使用英文表头的测试 Excel 文件
        MultipartFile excelFile = createTestExcelFileWithEnglishHeaders();
        
        // 执行导入
        ChecklistTemplate template = excelImportService.importTemplateFromExcel(
            excelFile, "Test Template", "Test Type");
        
        // 验证结果
        assertNotNull(template);
        assertEquals("Test Template", template.getName());
        assertEquals("Test Type", template.getType());
        
        // 验证检查项
        List<ChecklistItem> items = template.getItems();
        assertNotNull(items);
        assertEquals(2, items.size());
        
        ChecklistItem item1 = items.get(0);
        assertEquals(1, item1.getSequence());
        assertEquals("Check Item 1", item1.getContent());
        
        ChecklistItem item2 = items.get(1);
        assertEquals(2, item2.getSequence());
        assertEquals("Check Item 2", item2.getContent());
    }
    
    /**
     * 创建测试用的 Excel 文件
     */
    private MultipartFile createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("序号");
        headerRow.createCell(1).setCellValue("内容");
        headerRow.createCell(2).setCellValue("分类");
        headerRow.createCell(3).setCellValue("必填");
        
        // 创建数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue(1);
        row1.createCell(1).setCellValue("检查项1");
        row1.createCell(2).setCellValue("分类A");
        row1.createCell(3).setCellValue("是");
        
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue(2);
        row2.createCell(1).setCellValue("检查项2");
        row2.createCell(2).setCellValue("分类B");
        row2.createCell(3).setCellValue("否");
        
        Row row3 = sheet.createRow(3);
        row3.createCell(0).setCellValue(3);
        row3.createCell(1).setCellValue("检查项3");
        row3.createCell(2).setCellValue("");
        row3.createCell(3).setCellValue("");
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
    
    /**
     * 创建使用英文表头的测试 Excel 文件
     */
    private MultipartFile createTestExcelFileWithEnglishHeaders() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Test Data");
        
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("Sequence");
        headerRow.createCell(1).setCellValue("Content");
        headerRow.createCell(2).setCellValue("Category");
        headerRow.createCell(3).setCellValue("Required");
        
        // 创建数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue(1);
        row1.createCell(1).setCellValue("Check Item 1");
        row1.createCell(2).setCellValue("Category A");
        row1.createCell(3).setCellValue(true);
        
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue(2);
        row2.createCell(1).setCellValue("Check Item 2");
        row2.createCell(2).setCellValue("Category B");
        row2.createCell(3).setCellValue(false);
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
    
    /**
     * 创建没有表头的 Excel 文件
     */
    private MultipartFile createExcelFileWithoutHeader() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 只创建数据行，没有表头（使用不会被识别为表头的内容）
        Row row1 = sheet.createRow(0);
        row1.createCell(0).setCellValue("数据1");
        row1.createCell(1).setCellValue("数据2");
        
        Row row2 = sheet.createRow(1);
        row2.createCell(0).setCellValue("数据3");
        row2.createCell(1).setCellValue("数据4");
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
    
    /**
     * 创建没有内容列的 Excel 文件
     */
    private MultipartFile createExcelFileWithoutContentColumn() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 创建表头行，但没有内容列
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("序号");
        headerRow.createCell(1).setCellValue("分类");
        
        // 创建数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue(1);
        row1.createCell(1).setCellValue("分类A");
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
    
    /**
     * 创建没有有效内容的 Excel 文件
     */
    private MultipartFile createExcelFileWithEmptyContent() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("序号");
        headerRow.createCell(1).setCellValue("内容");
        
        // 创建空数据行
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue(1);
        row1.createCell(1).setCellValue(""); // 空内容
        
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue(2);
        row2.createCell(1).setCellValue("   "); // 只有空格
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();
        
        return new MockMultipartFile("file", "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            outputStream.toByteArray());
    }
}