package com.checklist.service;

import com.checklist.util.FileUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileStorageService 单元测试
 */
class FileStorageServiceTest {
    
    private FileStorageService storageService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        // 设置临时目录作为工作目录
        System.setProperty("user.dir", tempDir.toString());
        storageService = new FileStorageService();
    }
    
    @Test
    void testBackupFile_Success() throws IOException {
        // 创建测试文件
        String testContent = "这是测试内容";
        String sourceFilePath = tempDir.resolve("test.txt").toString();
        FileUtil.writeFile(sourceFilePath, testContent);
        
        // 备份文件
        String backupFilePath = storageService.backupFile(sourceFilePath);
        
        // 验证结果
        assertNotNull(backupFilePath);
        assertTrue(FileUtil.fileExists(backupFilePath));
        assertTrue(backupFilePath.contains(".backup_"));
        
        // 验证备份内容
        String backupContent = FileUtil.readFile(backupFilePath);
        assertEquals(testContent, backupContent);
        
        // 验证校验和文件存在
        String checksumFilePath = backupFilePath + ".checksum";
        assertTrue(FileUtil.fileExists(checksumFilePath));
    }
    
    @Test
    void testBackupFile_FileNotExists() {
        String nonExistentFile = tempDir.resolve("non-existent.txt").toString();
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.backupFile(nonExistentFile)
        );
        assertEquals("源文件不存在: " + nonExistentFile, exception.getMessage());
    }
    
    @Test
    void testBackupFile_EmptyPath() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.backupFile("")
        );
        assertEquals("源文件路径不能为空", exception.getMessage());
    }
    
    @Test
    void testBackupFiles_Success() throws IOException {
        // 创建多个测试文件
        String file1Path = tempDir.resolve("test1.txt").toString();
        String file2Path = tempDir.resolve("test2.txt").toString();
        FileUtil.writeFile(file1Path, "内容1");
        FileUtil.writeFile(file2Path, "内容2");
        
        List<String> sourceFiles = Arrays.asList(file1Path, file2Path);
        
        // 批量备份
        Map<String, String> backupMap = storageService.backupFiles(sourceFiles);
        
        // 验证结果
        assertEquals(2, backupMap.size());
        assertTrue(backupMap.containsKey(file1Path));
        assertTrue(backupMap.containsKey(file2Path));
        
        // 验证备份文件存在
        for (String backupPath : backupMap.values()) {
            assertTrue(FileUtil.fileExists(backupPath));
        }
    }
    
    @Test
    void testBackupFiles_EmptyList() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.backupFiles(Arrays.asList())
        );
        assertEquals("源文件路径列表不能为空", exception.getMessage());
    }
    
    @Test
    void testRestoreFile_Success() throws IOException {
        // 创建测试文件并备份
        String testContent = "原始内容";
        String sourceFilePath = tempDir.resolve("original.txt").toString();
        FileUtil.writeFile(sourceFilePath, testContent);
        String backupFilePath = storageService.backupFile(sourceFilePath);
        
        // 修改原文件
        FileUtil.writeFile(sourceFilePath, "修改后的内容");
        
        // 恢复文件
        String targetFilePath = tempDir.resolve("restored.txt").toString();
        storageService.restoreFile(backupFilePath, targetFilePath);
        
        // 验证恢复的内容
        String restoredContent = FileUtil.readFile(targetFilePath);
        assertEquals(testContent, restoredContent);
    }
    
    @Test
    void testRestoreFile_BackupNotExists() {
        String nonExistentBackup = tempDir.resolve("non-existent.backup").toString();
        String targetPath = tempDir.resolve("target.txt").toString();
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.restoreFile(nonExistentBackup, targetPath)
        );
        assertEquals("备份文件不存在: " + nonExistentBackup, exception.getMessage());
    }
    
    @Test
    void testGetFileBackups_Success() throws IOException {
        // 创建测试文件（使用唯一名称）
        String sourceFilePath = tempDir.resolve("backup_list_test.txt").toString();
        FileUtil.writeFile(sourceFilePath, "测试内容");
        
        // 创建多个备份
        String backup1 = storageService.backupFile(sourceFilePath);
        
        // 稍等一下确保时间不同
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        String backup2 = storageService.backupFile(sourceFilePath);
        
        // 获取备份列表
        List<FileStorageService.BackupInfo> backups = storageService.getFileBackups(sourceFilePath);
        
        // 验证结果（至少有2个备份）
        assertTrue(backups.size() >= 2);
        
        // 验证备份信息
        for (FileStorageService.BackupInfo backup : backups) {
            assertNotNull(backup.getBackupFilePath());
            assertEquals(sourceFilePath, backup.getSourceFilePath());
            assertNotNull(backup.getBackupTime());
            assertTrue(backup.getFileSize() > 0);
            assertTrue(backup.isIntegrityValid());
        }
        
        // 验证按时间倒序排列
        assertTrue(backups.get(0).getBackupTime().isAfter(backups.get(1).getBackupTime()) ||
                  backups.get(0).getBackupTime().equals(backups.get(1).getBackupTime()));
    }
    
    @Test
    void testGetFileBackups_NoBackups() throws IOException {
        String sourceFilePath = tempDir.resolve("no-backup.txt").toString();
        List<FileStorageService.BackupInfo> backups = storageService.getFileBackups(sourceFilePath);
        
        assertTrue(backups.isEmpty());
    }
    
    @Test
    void testCleanupExpiredBackups_Success() throws IOException {
        // 创建测试文件
        String sourceFilePath = tempDir.resolve("cleanup_test.txt").toString();
        FileUtil.writeFile(sourceFilePath, "测试内容");
        
        // 创建备份
        String backupFilePath = storageService.backupFile(sourceFilePath);
        
        // 验证备份存在
        assertTrue(FileUtil.fileExists(backupFilePath));
        
        // 清理0天前的备份（应该清理所有备份）
        int cleanedCount = storageService.cleanupExpiredBackups(sourceFilePath, 0);
        
        // 验证清理结果（至少清理了1个）
        assertTrue(cleanedCount >= 1);
        assertFalse(FileUtil.fileExists(backupFilePath));
    }
    
    @Test
    void testCleanupExpiredBackups_NegativeRetentionDays() {
        String sourceFilePath = tempDir.resolve("test.txt").toString();
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.cleanupExpiredBackups(sourceFilePath, -1)
        );
        assertEquals("保留天数不能为负数", exception.getMessage());
    }
    
    @Test
    void testVerifyFileIntegrity_Valid() throws IOException {
        // 创建测试文件并备份
        String sourceFilePath = tempDir.resolve("test.txt").toString();
        FileUtil.writeFile(sourceFilePath, "测试内容");
        String backupFilePath = storageService.backupFile(sourceFilePath);
        
        // 验证完整性
        boolean isValid = storageService.verifyFileIntegrity(backupFilePath);
        assertTrue(isValid);
    }
    
    @Test
    void testVerifyFileIntegrity_Invalid() throws IOException {
        // 创建测试文件并备份
        String sourceFilePath = tempDir.resolve("test.txt").toString();
        FileUtil.writeFile(sourceFilePath, "测试内容");
        String backupFilePath = storageService.backupFile(sourceFilePath);
        
        // 修改备份文件内容（破坏完整性）
        FileUtil.writeFile(backupFilePath, "被修改的内容");
        
        // 验证完整性
        boolean isValid = storageService.verifyFileIntegrity(backupFilePath);
        assertFalse(isValid);
    }
    
    @Test
    void testVerifyFileIntegrity_NoChecksumFile() throws IOException {
        // 创建测试文件（没有校验和文件）
        String testFilePath = tempDir.resolve("test.txt").toString();
        FileUtil.writeFile(testFilePath, "测试内容");
        
        // 验证完整性（应该返回true，向后兼容）
        boolean isValid = storageService.verifyFileIntegrity(testFilePath);
        assertTrue(isValid);
    }
    
    @Test
    void testVerifyFilesIntegrity_Success() throws IOException {
        // 创建多个测试文件
        String file1Path = tempDir.resolve("test1.txt").toString();
        String file2Path = tempDir.resolve("test2.txt").toString();
        FileUtil.writeFile(file1Path, "内容1");
        FileUtil.writeFile(file2Path, "内容2");
        
        // 备份文件
        String backup1 = storageService.backupFile(file1Path);
        String backup2 = storageService.backupFile(file2Path);
        
        // 修改其中一个备份文件
        FileUtil.writeFile(backup2, "被修改的内容");
        
        // 批量验证完整性
        List<String> filesToVerify = Arrays.asList(backup1, backup2);
        Map<String, Boolean> results = storageService.verifyFilesIntegrity(filesToVerify);
        
        // 验证结果
        assertEquals(2, results.size());
        assertTrue(results.get(backup1));
        assertFalse(results.get(backup2));
    }
    
    @Test
    void testGetStorageStatistics_Success() throws IOException {
        // 创建一些测试文件和备份
        String file1Path = tempDir.resolve("data").resolve("templates").resolve("stats_test1.json").toString();
        String file2Path = tempDir.resolve("data").resolve("reviews").resolve("stats_test2.json").toString();
        
        FileUtil.writeFile(file1Path, "{\"test\": \"data1\"}");
        FileUtil.writeFile(file2Path, "{\"test\": \"data2\"}");
        
        storageService.backupFile(file1Path);
        storageService.backupFile(file2Path);
        
        // 获取统计信息
        FileStorageService.StorageStatistics stats = storageService.getStorageStatistics();
        
        // 验证结果（由于可能有其他测试的文件，只验证基本功能）
        assertNotNull(stats);
        assertTrue(stats.getTotalBackupFiles() >= 2);
        assertTrue(stats.getTotalBackupSize() > 0);
        assertTrue(stats.getTotalDataFiles() >= 2);
        assertTrue(stats.getTotalDataSize() > 0);
        assertNotNull(stats.getLastUpdateTime());
    }
    
    @Test
    void testSaveAndLoadConfig_Success() throws IOException {
        // 创建测试配置对象
        TestConfig config = new TestConfig();
        config.setName("测试配置");
        config.setValue(123);
        config.setEnabled(true);
        
        // 保存配置
        storageService.saveConfig("test-config", config);
        
        // 加载配置
        TestConfig loadedConfig = storageService.loadConfig("test-config", TestConfig.class);
        
        // 验证结果
        assertNotNull(loadedConfig);
        assertEquals("测试配置", loadedConfig.getName());
        assertEquals(123, loadedConfig.getValue());
        assertTrue(loadedConfig.isEnabled());
    }
    
    @Test
    void testLoadConfig_NotExists() throws IOException {
        TestConfig config = storageService.loadConfig("non-existent-config", TestConfig.class);
        assertNull(config);
    }
    
    @Test
    void testSaveConfig_NullConfigName() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.saveConfig(null, new TestConfig())
        );
        assertEquals("配置名称不能为空", exception.getMessage());
    }
    
    @Test
    void testSaveConfig_NullConfigData() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> storageService.saveConfig("test", null)
        );
        assertEquals("配置数据不能为空", exception.getMessage());
    }
    
    /**
     * 测试配置类
     */
    public static class TestConfig {
        private String name;
        private int value;
        private boolean enabled;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public int getValue() {
            return value;
        }
        
        public void setValue(int value) {
            this.value = value;
        }
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
}