package com.checklist.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileUtil 工具类测试
 */
class FileUtilTest {
    
    @TempDir
    Path tempDir;
    
    private String testFilePath;
    private String testContent;
    
    @BeforeEach
    void setUp() {
        testFilePath = tempDir.resolve("test.txt").toString();
        testContent = "这是测试内容\n包含中文字符";
    }
    
    @Test
    void testWriteAndReadFile() throws IOException {
        FileUtil.writeFile(testFilePath, testContent);
        String readContent = FileUtil.readFile(testFilePath);
        
        assertEquals(testContent, readContent);
    }
    
    @Test
    void testReadNonExistentFile() throws IOException {
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        String content = FileUtil.readFile(nonExistentPath);
        
        assertNull(content);
    }
    
    @Test
    void testWriteAndReadFileWithLock() throws IOException {
        FileUtil.writeFileWithLock(testFilePath, testContent);
        String readContent = FileUtil.readFileWithLock(testFilePath);
        
        assertEquals(testContent, readContent);
    }
    
    @Test
    void testReadNonExistentFileWithLock() throws IOException {
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        String content = FileUtil.readFileWithLock(nonExistentPath);
        
        assertNull(content);
    }
    
    @Test
    void testSafeWriteFile() throws IOException {
        // 先写入初始内容
        FileUtil.writeFile(testFilePath, "初始内容");
        
        // 使用安全写入更新内容
        FileUtil.safeWriteFile(testFilePath, testContent);
        String readContent = FileUtil.readFile(testFilePath);
        
        assertEquals(testContent, readContent);
    }
    
    @Test
    void testCreateBackup() throws IOException {
        FileUtil.writeFile(testFilePath, testContent);
        FileUtil.createBackup(testFilePath);
        
        // 检查备份文件是否存在
        String parentDir = tempDir.toString();
        assertTrue(FileUtil.fileExists(testFilePath));
        
        // 备份文件应该存在（文件名包含时间戳）
        // 这里只验证原文件仍然存在
        String readContent = FileUtil.readFile(testFilePath);
        assertEquals(testContent, readContent);
    }
    
    @Test
    void testCreateBackupNonExistentFile() throws IOException {
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        // 不应该抛出异常
        assertDoesNotThrow(() -> FileUtil.createBackup(nonExistentPath));
    }
    
    @Test
    void testFileExists() throws IOException {
        assertFalse(FileUtil.fileExists(testFilePath));
        
        FileUtil.writeFile(testFilePath, testContent);
        assertTrue(FileUtil.fileExists(testFilePath));
    }
    
    @Test
    void testDeleteFile() throws IOException {
        FileUtil.writeFile(testFilePath, testContent);
        assertTrue(FileUtil.fileExists(testFilePath));
        
        FileUtil.deleteFile(testFilePath);
        assertFalse(FileUtil.fileExists(testFilePath));
    }
    
    @Test
    void testDeleteNonExistentFile() {
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        // 不应该抛出异常
        assertDoesNotThrow(() -> FileUtil.deleteFile(nonExistentPath));
    }
    
    @Test
    void testCreateDirectories() throws IOException {
        String dirPath = tempDir.resolve("subdir/nested").toString();
        FileUtil.createDirectories(dirPath);
        
        assertTrue(FileUtil.fileExists(dirPath));
    }
    
    @Test
    void testGetFileSize() throws IOException {
        FileUtil.writeFile(testFilePath, testContent);
        long size = FileUtil.getFileSize(testFilePath);
        
        assertTrue(size > 0);
        assertEquals(testContent.getBytes("UTF-8").length, size);
    }
    
    @Test
    void testGetFileSizeNonExistent() throws IOException {
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        long size = FileUtil.getFileSize(nonExistentPath);
        
        assertEquals(0, size);
    }
    
    @Test
    void testGetLastModifiedTime() throws IOException {
        LocalDateTime beforeWrite = LocalDateTime.now().minusSeconds(1);
        FileUtil.writeFile(testFilePath, testContent);
        LocalDateTime afterWrite = LocalDateTime.now().plusSeconds(1);
        
        LocalDateTime modifiedTime = FileUtil.getLastModifiedTime(testFilePath);
        
        assertNotNull(modifiedTime);
        assertTrue(modifiedTime.isAfter(beforeWrite));
        assertTrue(modifiedTime.isBefore(afterWrite));
    }
    
    @Test
    void testGetLastModifiedTimeNonExistent() throws IOException {
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        LocalDateTime modifiedTime = FileUtil.getLastModifiedTime(nonExistentPath);
        
        assertNull(modifiedTime);
    }
    
    @Test
    void testCleanupBackups() throws IOException {
        FileUtil.writeFile(testFilePath, testContent);
        
        // 创建备份
        FileUtil.createBackup(testFilePath);
        
        // 清理备份（保留0天，即立即清理）
        FileUtil.cleanupBackups(testFilePath, 0);
        
        // 原文件应该仍然存在
        assertTrue(FileUtil.fileExists(testFilePath));
    }
    
    @Test
    void testEmptyContent() throws IOException {
        String emptyContent = "";
        FileUtil.writeFile(testFilePath, emptyContent);
        String readContent = FileUtil.readFile(testFilePath);
        
        assertEquals(emptyContent, readContent);
    }
    
    @Test
    void testUnicodeContent() throws IOException {
        String unicodeContent = "测试中文内容 🚀 emoji 和特殊字符 ñáéíóú";
        FileUtil.writeFile(testFilePath, unicodeContent);
        String readContent = FileUtil.readFile(testFilePath);
        
        assertEquals(unicodeContent, readContent);
    }
}