package com.checklist.util;

import com.checklist.model.ChecklistItem;
import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ReviewStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JsonUtil 工具类测试
 */
class JsonUtilTest {
    
    @Test
    void testToJson() throws JsonProcessingException {
        ChecklistItem item = new ChecklistItem("item1", 1, "测试项", true, "category1");
        String json = JsonUtil.toJson(item);
        
        assertNotNull(json);
        assertTrue(json.contains("item1"));
        assertTrue(json.contains("测试项"));
        assertTrue(json.contains("category1"));
    }
    
    @Test
    void testToJsonNull() throws JsonProcessingException {
        String json = JsonUtil.toJson(null);
        assertNull(json);
    }
    
    @Test
    void testFromJson() throws JsonProcessingException {
        String json = "{\"id\":\"item1\",\"sequence\":1,\"content\":\"测试项\",\"required\":true,\"category\":\"category1\"}";
        ChecklistItem item = JsonUtil.fromJson(json, ChecklistItem.class);
        
        assertNotNull(item);
        assertEquals("item1", item.getId());
        assertEquals(Integer.valueOf(1), item.getSequence());
        assertEquals("测试项", item.getContent());
        assertEquals(Boolean.TRUE, item.getRequired());
        assertEquals("category1", item.getCategory());
    }
    
    @Test
    void testFromJsonNull() throws JsonProcessingException {
        ChecklistItem item = JsonUtil.fromJson(null, ChecklistItem.class);
        assertNull(item);
        
        ChecklistItem item2 = JsonUtil.fromJson("", ChecklistItem.class);
        assertNull(item2);
        
        ChecklistItem item3 = JsonUtil.fromJson("   ", ChecklistItem.class);
        assertNull(item3);
    }
    
    @Test
    void testFromJsonWithTypeReference() throws JsonProcessingException {
        String json = "[{\"id\":\"item1\",\"sequence\":1,\"content\":\"测试项1\",\"required\":true,\"category\":\"category1\"}," +
                     "{\"id\":\"item2\",\"sequence\":2,\"content\":\"测试项2\",\"required\":false,\"category\":\"category2\"}]";
        
        List<ChecklistItem> items = JsonUtil.fromJson(json, new TypeReference<List<ChecklistItem>>() {});
        
        assertNotNull(items);
        assertEquals(2, items.size());
        assertEquals("item1", items.get(0).getId());
        assertEquals("item2", items.get(1).getId());
    }
    
    @Test
    void testConvertValue() {
        ChecklistItem item = new ChecklistItem("item1", 1, "测试项", true, "category1");
        ChecklistItem converted = JsonUtil.convertValue(item, ChecklistItem.class);
        
        assertNotNull(converted);
        assertEquals(item.getId(), converted.getId());
        assertEquals(item.getSequence(), converted.getSequence());
        assertEquals(item.getContent(), converted.getContent());
    }
    
    @Test
    void testConvertValueNull() {
        ChecklistItem converted = JsonUtil.convertValue(null, ChecklistItem.class);
        assertNull(converted);
    }
    
    @Test
    void testIsValidJson() {
        assertTrue(JsonUtil.isValidJson("{\"key\":\"value\"}"));
        assertTrue(JsonUtil.isValidJson("[1,2,3]"));
        assertTrue(JsonUtil.isValidJson("\"string\""));
        assertTrue(JsonUtil.isValidJson("123"));
        assertTrue(JsonUtil.isValidJson("true"));
        
        assertFalse(JsonUtil.isValidJson("{invalid json}"));
        assertFalse(JsonUtil.isValidJson(""));
        assertFalse(JsonUtil.isValidJson(null));
        assertFalse(JsonUtil.isValidJson("   "));
    }
    
    @Test
    void testPrettyPrint() throws JsonProcessingException {
        String compactJson = "{\"id\":\"item1\",\"sequence\":1,\"content\":\"测试项\"}";
        String prettyJson = JsonUtil.prettyPrint(compactJson);
        
        assertNotNull(prettyJson);
        assertTrue(prettyJson.contains("\n"));
        assertTrue(prettyJson.contains("  "));
    }
    
    @Test
    void testPrettyPrintNull() throws JsonProcessingException {
        assertNull(JsonUtil.prettyPrint(null));
        assertEquals("", JsonUtil.prettyPrint(""));
    }
    
    @Test
    void testComplexObjectSerialization() throws JsonProcessingException {
        LocalDateTime now = LocalDateTime.now();
        ChecklistItem item1 = new ChecklistItem("item1", 1, "测试项1", true, "category1");
        ChecklistItem item2 = new ChecklistItem("item2", 2, "测试项2", false, "category2");
        List<ChecklistItem> items = Arrays.asList(item1, item2);
        
        ChecklistTemplate template = new ChecklistTemplate("template1", "测试模板", "type1", "1.0", now, now, items);
        
        String json = JsonUtil.toJson(template);
        assertNotNull(json);
        
        ChecklistTemplate deserialized = JsonUtil.fromJson(json, ChecklistTemplate.class);
        assertNotNull(deserialized);
        assertEquals(template.getId(), deserialized.getId());
        assertEquals(template.getName(), deserialized.getName());
        assertEquals(template.getItems().size(), deserialized.getItems().size());
    }
    
    @Test
    void testEnumSerialization() throws JsonProcessingException {
        ReviewStatus status = ReviewStatus.PASS;
        String json = JsonUtil.toJson(status);
        assertEquals("\"PASS\"", json);
        
        ReviewStatus deserialized = JsonUtil.fromJson(json, ReviewStatus.class);
        assertEquals(ReviewStatus.PASS, deserialized);
    }
}