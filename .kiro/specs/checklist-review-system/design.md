# 设计文档

## 概述

本系统采用前后端分离的架构设计，后端使用 Java Spring Boot 框架提供 REST API 服务，前端使用现代 Web 技术栈构建用户界面。数据存储采用 JSON 文件格式，避免对数据库的依赖，简化部署和维护。

系统分为两个主要模块：
- **后台管理端**：用于管理 checklist 模板、配置和系统设置
- **前台评审端**：用于实际的评审操作和结果查看

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[后台管理界面] 
        B[前台评审界面]
    end
    
    subgraph "后端层"
        C[Spring MVC Application]
        D[REST API Controller]
        E[Service Layer]
        F[Repository Layer]
    end
    
    subgraph "数据层"
        G[JSON 文件存储]
        H[模板文件]
        I[评审数据文件]
    end
    
    A --> D
    B --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
```

### 技术栈选择

**后端技术栈：**
- Java 11
- Spring MVC 5.x
- Spring Core + Spring Web
- Jackson (JSON 处理)
- Apache POI (Excel 文件处理)
- Maven (项目管理)
- Tomcat (Web 容器)

**前端技术栈：**
- Vue.js 3.x (渐进式框架)
- Vue Router (路由管理)
- Axios (HTTP 客户端)
- Element Plus (UI 组件库)
- Vite (构建工具)

## 组件和接口设计

### 后端组件设计

#### 1. Controller 层

**ChecklistTemplateController**
```java
@RestController
@RequestMapping("/api/templates")
public class ChecklistTemplateController {
    // 获取所有模板
    GET /api/templates
    
    // 根据类型获取模板
    GET /api/templates/{type}
    
    // 创建新模板
    POST /api/templates
    
    // 更新模板
    PUT /api/templates/{id}
    
    // 删除模板
    DELETE /api/templates/{id}
    
    // Excel 导入
    POST /api/templates/import
}
```

**ChecklistReviewController**
```java
@RestController
@RequestMapping("/api/reviews")
public class ChecklistReviewController {
    // 创建评审实例
    POST /api/reviews
    
    // 获取评审实例
    GET /api/reviews/{id}
    
    // 更新评审项状态
    PUT /api/reviews/{id}/items/{itemId}
    
    // 批量更新评审项
    PUT /api/reviews/{id}/items/batch
    
    // 获取评审历史
    GET /api/reviews/{id}/history
}
```

#### 2. Service 层

**ChecklistTemplateService**
- 模板的 CRUD 操作
- Excel 文件解析和导入
- 模板验证和格式化

**ChecklistReviewService**
- 评审实例管理
- 评审状态更新
- 多人协作处理
- 评审历史记录

**FileStorageService**
- JSON 文件读写操作
- 文件备份和恢复
- 并发访问控制

#### 3. Repository 层

**JsonFileRepository**
- 通用 JSON 文件操作接口
- 文件锁机制防止并发冲突
- 数据序列化和反序列化

### 前端组件设计

#### 1. 后台管理界面 (Vue.js 组件)

**模板管理页面组件**
- `TemplateList.vue` - 模板列表展示
- `TemplateForm.vue` - 模板创建/编辑表单
- `ExcelImport.vue` - Excel 导入功能
- `ItemConfig.vue` - 检查项配置界面

**系统配置页面组件**
- `TypeManagement.vue` - 评审类型管理
- `UserPermission.vue` - 用户权限设置
- `SystemConfig.vue` - 系统参数配置

#### 2. 前台评审界面 (Vue.js 组件)

**评审主界面组件**
- `ChecklistReview.vue` - 主评审页面
- `ReviewItem.vue` - 单个检查项组件
- `BatchOperations.vue` - 批量操作工具栏
- `ProgressStats.vue` - 进度统计组件

**评审历史页面组件**
- `ReviewHistory.vue` - 历史记录主页面
- `HistoryItem.vue` - 历史记录项组件
- `ReviewerInfo.vue` - 评审人员信息组件

## 数据模型设计

### 核心数据模型

#### ChecklistTemplate (检查单模板)
```json
{
  "id": "string",
  "name": "string",
  "type": "string",
  "version": "string",
  "createdTime": "datetime",
  "updatedTime": "datetime",
  "items": [
    {
      "id": "string",
      "sequence": "number",
      "content": "string",
      "required": "boolean",
      "category": "string"
    }
  ]
}
```

#### ChecklistReview (评审实例)
```json
{
  "id": "string",
  "templateId": "string",
  "templateVersion": "string",
  "type": "string",
  "createdTime": "datetime",
  "status": "string",
  "reviewItems": [
    {
      "itemId": "string",
      "status": "string", // PASS, FAIL, SKIP, PENDING
      "comment": "string",
      "reviewHistory": [
        {
          "reviewer": "string",
          "reviewTime": "datetime",
          "status": "string",
          "comment": "string"
        }
      ]
    }
  ]
}
```

#### ReviewHistory (评审历史)
```json
{
  "reviewId": "string",
  "sequence": "number",
  "reviewer": "string",
  "reviewTime": "datetime",
  "action": "string",
  "details": "object"
}
```

### 项目结构

#### 后端项目结构 (Spring MVC)
```
checklist-backend/
├── src/main/java/
│   └── com/checklist/
│       ├── config/
│       │   ├── WebConfig.java
│       │   └── AppConfig.java
│       ├── controller/
│       │   ├── ChecklistTemplateController.java
│       │   └── ChecklistReviewController.java
│       ├── service/
│       │   ├── ChecklistTemplateService.java
│       │   ├── ChecklistReviewService.java
│       │   └── FileStorageService.java
│       ├── repository/
│       │   └── JsonFileRepository.java
│       ├── model/
│       │   ├── ChecklistTemplate.java
│       │   ├── ChecklistReview.java
│       │   └── ReviewHistory.java
│       └── exception/
│           └── GlobalExceptionHandler.java
├── src/main/resources/
│   └── applicationContext.xml
└── src/main/webapp/
    └── WEB-INF/
        ├── web.xml
        └── dispatcher-servlet.xml
```

#### 前端项目结构 (Vue.js)
```
checklist-frontend/
├── src/
│   ├── components/
│   │   ├── admin/
│   │   │   ├── TemplateList.vue
│   │   │   ├── TemplateForm.vue
│   │   │   └── ExcelImport.vue
│   │   └── review/
│   │       ├── ChecklistReview.vue
│   │       ├── ReviewItem.vue
│   │       └── BatchOperations.vue
│   ├── views/
│   │   ├── AdminDashboard.vue
│   │   └── ReviewDashboard.vue
│   ├── router/
│   │   └── index.js
│   ├── api/
│   │   ├── template.js
│   │   └── review.js
│   ├── utils/
│   │   └── request.js
│   └── main.js
├── public/
└── package.json
```

### 文件存储结构

```
data/
├── templates/
│   ├── template_001.json
│   ├── template_002.json
│   └── ...
├── reviews/
│   ├── review_001.json
│   ├── review_002.json
│   └── ...
├── config/
│   ├── system_config.json
│   └── user_config.json
└── backup/
    ├── templates/
    └── reviews/
```

## 错误处理设计

### 异常处理策略

1. **全局异常处理器**
   - 统一的错误响应格式
   - 错误码标准化
   - 日志记录和监控

2. **业务异常类型**
   - `TemplateNotFoundException`
   - `ReviewNotFoundException`
   - `FileAccessException`
   - `ValidationException`

3. **错误响应格式**
```json
{
  "success": false,
  "errorCode": "string",
  "message": "string",
  "timestamp": "datetime",
  "path": "string"
}
```

### 数据一致性保证

1. **文件锁机制**
   - 读写操作加锁
   - 超时处理
   - 死锁检测

2. **数据备份策略**
   - 操作前自动备份
   - 定期备份清理
   - 数据恢复机制

## 测试策略

### 单元测试

1. **Service 层测试**
   - 业务逻辑验证
   - 边界条件测试
   - 异常情况处理

2. **Repository 层测试**
   - 文件操作测试
   - 并发访问测试
   - 数据完整性验证

### 集成测试

1. **API 接口测试**
   - REST API 功能验证
   - 参数验证测试
   - 错误响应测试

2. **端到端测试**
   - 完整业务流程测试
   - 多用户协作测试
   - 性能压力测试

### 测试数据管理

1. **测试数据准备**
   - 模板数据生成
   - 评审数据模拟
   - 边界数据构造

2. **测试环境隔离**
   - 独立的测试数据目录
   - 测试后数据清理
   - 测试配置管理